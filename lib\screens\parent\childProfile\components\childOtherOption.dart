import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/widget/fileUpload.dart';
import 'package:seawork/screens/parent/childProfile/components/childEmiratesIDUpload.dart';
import 'package:seawork/screens/parent/childProfile/components/popupConfirm.dart';
import 'package:seawork/screens/parent/kid/models/kidInfoModel.dart';
import 'package:seawork/screens/parent/kid/repository/kidAddRepositorty.dart';
import 'package:seawork/utils/style/colors.dart';

class ChildOtherOption extends ConsumerStatefulWidget {
  final Function(bool isCompleted) onComplete;

  const ChildOtherOption({Key? key, required this.onComplete})
    : super(key: key);

  @override
  ConsumerState<ChildOtherOption> createState() => _ChildOtherOptionState();
}

class _ChildOtherOptionState extends ConsumerState<ChildOtherOption>
    with WidgetsBindingObserver {
  String? uploadedFileName;
  String? uploadedFileSize;
  String? birthCertificateFileId;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _handleSaveAndContinue() async {
    if (_isLoading) return;

    if (uploadedFileName == null) {
      _showErrorSnackbar('Please upload a birth certificate');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final kidInfo = _buildKidInfo();
      final repository = ref.read(kidAddRepositoryProvider);
      final response = await _executeApiCall(repository, kidInfo);

      if (!_isValidResponse(response)) {
        throw Exception('API response was incomplete');
      }

      _handleSuccess();
    } catch (e, stack) {
      _handleError(e, stack);
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  bool _isValidResponse(KidInfo? response) {
    // Consider it valid if we have either an ID or other identifying information
    return response != null &&
        (response.id != null ||
            response.fullNameInEnglish != null ||
            response.emiratesNo != null);
  }

  KidInfo _buildKidInfo() {
    return KidInfo(
      id: 0,
      fullNameInEnglish: ref.read(childNameEnProvider).toStringSafe(),
      fullNameInArabic: ref.read(childNameArProvider).toStringSafe(),
      gender: ref.read(childGenderProvider).toStringSafe().toLowerCase(),
      dob: formatDate(DateTime(2016, 6, 4)),
      nationality: ref.read(childNationalityProvider).toStringSafe("Other"),
      emiratesNo: ref
          .read(childIdNumberProvider)
          .toStringSafe("784201844553989"),
      emiratesExpiry: formatDate(DateTime(2026, 6, 4)),
      emiratesDocumentUploadFront:
          ref.read(childEmiratesIdFileProvider)?.path.toStringSafe(),
      emiratesDocumentUploadBack: "",
      familyBookAttachment: birthCertificateFileId.toStringSafe(),
      profileDocumentUpload:
          ref.read(childProfileImageProvider)?.path.toStringSafe(),
      isDisabled: false,
      isVerificationStarted: false,
      familyId: 3406,
      currentWizardNo: 2,
      profileReviewStage: 1,
    );
  }

  Future<KidInfo?> _executeApiCall(
    KidAddRepository repository,
    KidInfo kidInfo,
  ) async {
    try {
      final response = await repository.addKidConsent(kidInfo);
      return response;
    } on DioException catch (dioError) {
      throw Exception('Network error: ${dioError.message}');
    } catch (e) {
      rethrow;
    }
  }

  void _handleSuccess() {
    widget.onComplete(true);
    _showSuccessSnackbar('Data saved successfully!');
  }

  void _handleError(dynamic e, StackTrace stack) {
    String errorMessage = 'An error occurred';

    if (e.toString().contains('201')) {
      errorMessage = 'Data created successfully';
      widget.onComplete(true);
      return;
    } else if (e is DioException) {
      errorMessage = 'Network error: ${e.message}';
    } else if (e is FormatException) {
      errorMessage = 'Data format error. Please try again.';
    } else {
      errorMessage = e.toString().split('\n').first;
    }

    _showErrorSnackbar('Error: $errorMessage');
  }

  void _showSuccessSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: DmSansText(message),
        backgroundColor: AppColors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: DmSansText(message),
        backgroundColor: AppColors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: DMSans600Medium(14, label, AppColors.blackColor),
          ),
          Expanded(
            child: DmSansText(
              value?.isNotEmpty == true ? value! : 'Not provided',
              color: AppColors.blackColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            DMSans600Medium(16, 'Birth Certificate', AppColors.blackColor),
            const SizedBox(height: 8),
            FileUploadWidget(
              key: ValueKey(uploadedFileName),
              initialFileName: uploadedFileName,
              initialFileSize: uploadedFileSize,
              onFileSelected: (fileName, fileSize, fileId) {
                setState(() {
                  uploadedFileName = fileName;
                  uploadedFileSize = fileSize;
                  birthCertificateFileId = fileId as String?;
                });
              },
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _handleSaveAndContinue,
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 56),
                  backgroundColor: AppColors.viewColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child:
                    _isLoading
                        ? const CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        )
                        : DmSansText(
                          'Save and Continue',
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.whiteColor,
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _onWillPop() async {
    if (uploadedFileName != null) {
      return await confirmPopup(
            context: context,
            title: 'Leave this page?',
            content: 'Your changes are saved. You can return anytime.',
            positiveButtonText: 'Okay',
            negativeButtonText: 'Keep editing',
          ) ??
          false;
    }
    return true;
  }
}

final DateFormat _apiDateFormat = DateFormat("yyyy-MM-dd'T'HH:mm:ss");

String formatDate(dynamic date) {
  try {
    if (date == null) return _apiDateFormat.format(DateTime.now());
    if (date is DateTime) return _apiDateFormat.format(date);
    if (date is String) {
      final parsed = DateTime.tryParse(date);
      if (parsed != null) return _apiDateFormat.format(parsed);
      return _apiDateFormat.format(DateTime.now());
    }
    return _apiDateFormat.format(DateTime.now());
  } catch (e) {
    return _apiDateFormat.format(DateTime.now());
  }
}

extension SafeParsing on dynamic {
  int? toInt([int defaultValue = 0]) {
    try {
      if (this == null) return defaultValue;
      if (this is int) return this;
      if (this is double) return this.toInt();
      if (this is String) {
        return int.tryParse(this.replaceAll(RegExp(r'[^0-9]'), '')) ??
            defaultValue;
      }
      return defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }

  String toStringSafe([String defaultValue = '']) {
    try {
      return this?.toString().trim() ?? defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }

  bool toBool([bool defaultValue = false]) {
    try {
      if (this == null) return defaultValue;
      if (this is bool) return this;
      if (this is String) return this.toLowerCase() == 'true';
      return defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }
}
