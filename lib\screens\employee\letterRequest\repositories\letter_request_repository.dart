import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:seawork/screens/employee/approval/models/taskdetailhistorymodel.dart';
import 'package:seawork/screens/employee/letterRequest/models/document_record_attachment_response_model.dart';
import 'package:seawork/screens/employee/letterRequest/models/document_record_response_model.dart';
import 'package:seawork/screens/employee/letterRequest/models/document_record_statusCount_model.dart';
import 'package:seawork/screens/employee/letterRequest/models/letter_request_submit_model.dart';
import 'package:seawork/screens/employee/letterRequest/models/letter_request_types_model.dart';
import 'package:seawork/utils/downloadUtils.dart';
import 'package:universal_html/html.dart' as html;

class LetterRequestRepository {
  final Dio dio;
  final String baseUrlEMS;

  LetterRequestRepository(this.dio, this.baseUrlEMS);

  Future<List<LetterRequestTypesModel>> fetchDocumentsTypes() async {
    try {
      final response = await dio.get(
        '$baseUrlEMS/hrDocumentTypesLOV?limit=300',
      );

      if (response.statusCode == 200 &&
          response.data is Map<String, dynamic> &&
          response.data['items'] is List) {
        final List<dynamic> items = response.data['items'];
        return items
            .map((json) => LetterRequestTypesModel.fromJson(json))
            .toList();
      } else {
        throw Exception('Invalid response format');
      }
    } catch (e) {
      print("API Error: $e");
      rethrow;
    }
  }

  Future<LetterRequesSubmitModel> submitDocument(
    LetterRequesSubmitModel model,
  ) async {
    try {
      final response = await dio.post(
        '$baseUrlEMS/documentRecords', // Replace with the actual endpoint
        data: model.toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            // 'Authorization': 'Bearer YOUR_TOKEN', if needed
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return LetterRequesSubmitModel.fromJson(response.data);
      } else {
        throw Exception('Failed to submit document: ${response.statusCode}');
      }
    } catch (e) {
      print("API Error: $e");
      rethrow;
    }
  }

  Future<void> viewDraft(Map<String, dynamic> queryParameters) async {
    try {
      final response = await dio.get(
        '$baseUrlEMS/documentRecords/action/generateDraftLetter', // Replace with the actual endpoint
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes),
      );
      if (response.statusCode == 200 &&
          response.data != null &&
          response.data is List<int>) {
        if (kIsWeb) {
          openPdfInNewTab(Uint8List.fromList(response.data));
        } else {
          final dir = await getTemporaryDirectory();
          final file = File('${dir.path}/Draft_Letter.pdf');
          await file.writeAsBytes(response.data);
          OpenFile.open(file.path);
        }
      } else {
        print("Invalid response: ${response.statusCode}, ${response.data}");
      }
    } catch (e) {
      print("API Error: $e");
      rethrow;
    }
  }

  Future<DocumentResponse> getSingleDocumentRecord(int personId) async {
    try {
      final response = await dio.get(
        '$baseUrlEMS/documentRecords?q=PersonId=$personId&&limit=1000&orderBy=CreationDate:desc',
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            // 'Authorization': 'Bearer YOUR_TOKEN', if needed
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        // print(response.data);
        return DocumentResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to submit document: ${response.statusCode}');
      }
    } catch (e) {
      print("API Error: $e");
      rethrow;
    }
  }

  Future<DocumentStatusCount> getDocumentRecordStatus() async {
    try {
      final Map<String, dynamic> queryParameters = {
        'totalResults': true,
        'orderBy': 'CreationDate:desc',
        // 'finder': 'findByPersonIdAfterReload;PersonId=$personId',
      };
      final response = await dio.get(
        '$baseUrlEMS/documentRecords/status', // <-- updated
        queryParameters: queryParameters,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            // 'Authorization': 'Bearer YOUR_TOKEN', if needed
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return DocumentStatusCount.fromJson(response.data);
      } else {
        throw Exception('Failed to submit document: ${response.statusCode}');
      }
    } catch (e) {
      print("API Error: $e");
      rethrow;
    }
  }

  Future<TaskHistoryResponse> getTaskHistory(int taskId) async {
    try {
      final response = await dio.get(
        '$baseUrlEMS/tasks/$taskId/history', // <-- updated
        queryParameters: {'id': taskId},
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            // 'Authorization': 'Bearer YOUR_TOKEN', // Add if needed
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return TaskHistoryResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to fetch task history: ${response.statusCode}');
      }
    } catch (e) {
      print("Task History API Error: $e");
      rethrow;
    }
  }

  Future<DocumentAttachmentsResponse> getDocumentAttachments(
    int documentsOfRecordId,
  ) async {
    try {
      final response = await dio.get(
        '$baseUrlEMS/documentRecords/$documentsOfRecordId/child/attachments', // <-- updated
        queryParameters: {'documentsOfRecordId': documentsOfRecordId},
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            // 'Authorization': 'Bearer YOUR_TOKEN', // Add if needed
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return DocumentAttachmentsResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to fetch task history: ${response.statusCode}');
      }
    } catch (e) {
      print("Task History API Error: $e");
      rethrow;
    }
  }

  Future<void> downloadAndSaveAttachment({
    required String url,
    required String fileName,
    required dynamic context, // <-- add context parameter
  }) async {
    try {
      print('url issssss $url');
      await downloadFileWithDio(
        dio: dio,
        url: url,
        fileName: fileName,
        context: context, // <-- pass context here
      );
    } catch (e) {
      print("Download error: $e");
      rethrow;
    }
  }

  Future<DocumentResponse> getPaginatedDocuments(
    int personId, {
    int limit = 50,
    int offset = 0,
    String? filterType,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? status,
  }) async {
    try {
      Map<String, dynamic> queryParameters = {
        'limit': limit,
        'offset': offset,
        'expand': 'attachments,bannerOverrideMessages',
        'orderBy': 'CreationDate:desc',
        // 'finder': 'findByPersonIdAfterReload;PersonId=$personId',
        'status': status,
      };

      if (filterType != null) {
        switch (filterType) {
          case 'Open tickets':
            queryParameters['status_id'] = 1;
            break;
          case 'Closed tickets':
            queryParameters['status_id'] = 9;
            break;
          case 'Awaiting tickets':
            queryParameters['awaitinginput'] = '1';
            break;
          // 'All tickets' case doesn't need additional parameters
        }
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        queryParameters['search'] = searchQuery;
      }
      if (startDate != null) {
        queryParameters['startDate'] = DateFormat(
          'yyyy-MM-dd',
        ).format(startDate);
      }
      if (endDate != null) {
        queryParameters['endDate'] = DateFormat('yyyy-MM-dd').format(endDate);
      }
      final response = await dio.get(
        '$baseUrlEMS/documentRecords', // <-- updated
        queryParameters: queryParameters,
        options: Options(headers: {'Content-Type': 'application/json'}),
      );

      // Add type checking
      if (response.data is! Map<String, dynamic>) {
        throw Exception('Invalid API response format');
      }

      if (response.data == null) {
        return DocumentResponse(
          count: 0,
          hasMore: false,
          items: [],
          limit: 0,
          offset: 0,
          totalResults: 0,
        );
      }

      try {
        final documentList = DocumentResponse.fromJson(
          response.data as Map<String, dynamic>,
        );

        // if (pageNo >= 3 && documentList.items.isEmpty) {
        //   // API limitation - no more tickets available
        //   return documentList;
        // }

        return documentList;
      } catch (parseError) {
        print('Parse error: ${parseError.toString()}');
        return DocumentResponse(
          count: 0,
          hasMore: false,
          items: [],
          limit: 0,
          offset: 0,
          totalResults: 0,
        );
      }
    } catch (e) {
      print('Full error details: ${e.toString()}');
      return DocumentResponse(
        count: 0,
        hasMore: false,
        items: [],
        limit: 0,
        offset: 0,
        totalResults: 0,
      );
    }
  }
}

void openPdfInNewTab(Uint8List pdfBytes) {
  final blob = html.Blob([pdfBytes], 'application/pdf');
  final url = html.Url.createObjectUrlFromBlob(blob);
  html.window.open(url, "_blank");
  html.Url.revokeObjectUrl(url);
}

String generateUniqueFilePath(String directoryPath, String originalFileName) {
  final RegExp pattern = RegExp(r'^(.*?)(?: \((\d+)\))?(\.[^.]+)?$');
  final match = pattern.firstMatch(originalFileName);

  if (match == null) return '$directoryPath/$originalFileName';

  String baseName = match.group(1)!;
  int count = int.tryParse(match.group(2) ?? '') ?? 0;
  String extension = match.group(3) ?? '';

  String filePath = '$directoryPath/$originalFileName';
  File file = File(filePath);

  while (file.existsSync()) {
    count++;
    final newFileName = '$baseName ($count)$extension';
    filePath = '$directoryPath/$newFileName';
    file = File(filePath);
  }

  return filePath;
}
