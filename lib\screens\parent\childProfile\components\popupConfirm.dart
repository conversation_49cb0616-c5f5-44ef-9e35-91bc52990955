import 'package:flutter/material.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/utils/style/colors.dart';

Future<bool?> confirmPopup({
  required BuildContext context,
  String title = 'Leave this page?',
  String content = 'Your changes are saved. You can return anytime.',
  String positiveButtonText = 'Okay',
  String negativeButtonText = 'Keep editing',
  Color? positiveButtonColor,
  Color? negativeButtonBorderColor,
  double? dialogWidth, // optional: remove default fixed size
  double? dialogHeight, // optional: remove default fixed size
}) {
  return showDialog<bool>(
    context: context,
    builder: (context) {
      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;

      final double width =
          dialogWidth ?? screenWidth * 0.9; // 90% width if not passed
      final double height =
          dialogHeight ?? screenHeight * 0.3; // 30% height if not passed

      return Dialog(
        backgroundColor: AppColors.whiteColor,
        insetPadding: const EdgeInsets.symmetric(horizontal: 18, vertical: 26),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: SizedBox(
          width: width,
          height: height,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                DMSans600Medium(
                  18,
                  title,
                  AppColors.blackColor,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                OpenSans400Large(
                  14,
                  content,
                  AppColors.blackColor,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 48,
                      width: 140,
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: DMSans600Medium(
                          16,
                          negativeButtonText,
                          AppColors.viewColor,
                        ),
                        style: OutlinedButton.styleFrom(
                          backgroundColor: AppColors.whiteColor,
                          side: BorderSide(
                            color:
                                negativeButtonBorderColor ??
                                AppColors.viewColor,
                            width: 1,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      height: 48,
                      width: 140,
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: DMSans600Medium(
                          16,
                          positiveButtonText,
                          AppColors.whiteColor,
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              positiveButtonColor ?? AppColors.viewColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}
