import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/parent/family/models/uploadTos3ResponseModel.dart';
import 'package:seawork/screens/parent/family/repository/familyRepository.dart';
import 'package:seawork/screens/parent/family/models/familyModel.dart'
    as family_model;
import 'package:seawork/screens/parent/family/models/simpleFamily.dart'
    as simple_family_model;
import 'package:seawork/screens/parent/family/models/maritalStatusModel.dart';

final familyRepositoryProvider = Provider<FamilyRepository>(
  (ref) => FamilyRepository(ref.read(dioProvider)),
);

final addUpdateFamilyProvider =
    FutureProvider.family<family_model.FamilyModel, family_model.FamilyModel>((
      ref,
      body,
    ) async {
      final familyRepository = ref.watch(familyRepositoryProvider);
      final response = await familyRepository.addUpdateFamily(body);
      return response;
    });
final uploadTos3Provider = FutureProvider.family<UploadTos3, FormData>((
  ref,
  formData,
) async {
  final repository = ref.watch(
    familyRepositoryProvider,
  ); // Replace with your actual repository provider
  return await repository.uploadTos3(formData: formData);
});

final addUpdateSpouseProvider =
    FutureProvider.family<MaritalStatus, ({int? familyId, int? parentId})>((
      ref,
      params,
    ) async {
      final familyRepository = ref.watch(familyRepositoryProvider);
      final response = await familyRepository.addUpdateSpouse(
        familyId: params.familyId,
        parentId: params.parentId,
      );
      return response;
    });

final getFamilyDetailsByParentIdProvider = FutureProvider.family<
  List<family_model.FamilyModel>,
  ({
    String? fields,
    int? pageNumber,
    int? pageSize,
    int? id,
    String? orderBy,
    int? parentId,
  })
>((ref, params) async {
  final familyRepository = ref.watch(familyRepositoryProvider);
  final response = await familyRepository.getFamilyDetailsByParentId(
    fields: params.fields,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    id: params.id,
    orderBy: params.orderBy,
    parentId: params.parentId,
  );
  return response;
});

final removeFamilySpouseKidProvider = FutureProvider.family<
  family_model.FamilyModel,
  ({int? familyId, int? spouseId, int? kidId})
>((ref, params) async {
  final familyRepository = ref.watch(familyRepositoryProvider);
  final response = await familyRepository.removeFamilySpouseKid(
    familyId: params.familyId,
    spouseId: params.spouseId,
    kidId: params.kidId,
  );
  return response;
});

final getFamilyDetailsByParentSpouseIdProvider = FutureProvider.family<
  List<simple_family_model.SimpleFamily>,
  ({int? parentId})
>((ref, params) async {
  final familyRepository = ref.watch(familyRepositoryProvider);
  final response = await familyRepository.getFamilyDetailsByParentSpouseId(
    parentId: params.parentId,
  );
  return response;
});
final familyDetailsStateProvider =
    StateProvider<List<simple_family_model.SimpleFamily>>((ref) => []);

final getFamilyDetailsByFamilyIdProvider = FutureProvider.family<
  List<family_model.FamilyModel>,
  ({
    String? fields,
    int? pageNumber,
    int? pageSize,
    int? id,
    String? orderBy,
    int? familyId,
    int? parentId,
  })
>((ref, params) async {
  final familyRepository = ref.watch(familyRepositoryProvider);
  final response = await familyRepository.getFamilyDetailsByFamilyId(
    fields: params.fields,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    id: params.id,
    orderBy: params.orderBy,
    familyId: params.familyId,
    parentId: params.parentId,
  );
  return response;
});

final getFamilySummaryByFamilyIdProvider =
    FutureProvider.family<List<family_model.FamilyModel>, ({int? familyId})>((
      ref,
      params,
    ) async {
      final familyRepository = ref.watch(familyRepositoryProvider);
      final response = await familyRepository.getFamilySummaryByFamilyId(
        familyId: params.familyId,
      );
      return response;
    });
