import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/permission/permissions.dart';

import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customStepper.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/screens/employee/approval/components/customActionButton.dart';
import 'package:seawork/screens/employee/approval/models/taskdetailsmodel.dart';
import 'package:seawork/screens/employee/approval/providers/approvalsProvider.dart';
import 'package:seawork/screens/employee/approval/components/customRemarksField.dart';
// For preferences
import 'package:seawork/screens/employee/absence/models/getAbsences.dart';
// For personId
import 'package:seawork/components/commonWidgets/customText.dart'; // For custom text widgets
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/utils/style/sizeConfig.dart';
import 'approvalBottomSheetWidgets.dart';

class ApprovalBottomSheet extends ConsumerWidget {
  final int taskId;
  final String category;
  final PermissionChecker permissionChecker = PermissionChecker();

  ApprovalBottomSheet({
    super.key,
    required this.taskId,
    required this.category,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final taskDetailsAsync = ref.watch(taskDetailsProvider(taskId.toString()));
    final taskHistoryAsync = ref.watch(taskHistoryProvider(taskId.toString()));
    final List<FileModel> uploadedFiles = [];

    // Get the absence details using the direct provider if we have a task ID
    final directAbsenceAsync = taskDetailsAsync.when(
      data: (taskDetails) {
        if (taskDetails.identificationKey != null) {
          print(
            'Using direct provider with ID: ${taskDetails.identificationKey}',
          );
          return ref.watch(
            directAbsenceProvider(taskDetails.identificationKey!),
          );
        }
        return null;
      },
      loading: () => null,
      error: (err, stack) => null,
    );

    // For backward compatibility, keeping the original code
    final absenceDetailsAsync = taskDetailsAsync.when(
      data: (taskDetails) {
        if (taskDetails.identificationKey != null) {
          print(
            'Task details identificationKey: ${taskDetails.identificationKey}',
          );
          return ref.watch(
            absenceDetailsProvider(taskDetails.identificationKey!),
          );
        }
        print('No identificationKey found in task details');
        return null;
      },
      loading: () => null,
      error: (err, stack) {
        print('Error loading task details: $err');
        return null;
      },
    );

    // Debug output for directAbsenceAsync
    if (directAbsenceAsync != null) {
      directAbsenceAsync.when(
        data:
            (data) =>
                print('directAbsenceAsync data: ${data?.personAbsenceEntryId}'),
        loading: () => print('directAbsenceAsync is loading'),
        error: (err, stack) => print('directAbsenceAsync error: $err'),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.only(
          left: 10,
          top: 12,
          bottom: 20,
          right: 10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [CustomSvgImage(imageName: "Rectangle_icon")],
            ),
            const SizedBox(height: 16),
            // Header Section - Always show this
            _buildHeader(taskDetailsAsync.value),
            const SizedBox(height: 20),

            // Information Rows - Always show this
            _buildInfoSection(taskDetailsAsync.value, directAbsenceAsync),
            const SizedBox(height: 20),

            // Comments and Attachments Section with loading state
            _buildCommentsSection(directAbsenceAsync, context),
            const SizedBox(height: 20),

            // Approval Timeline - Always show this
            taskHistoryAsync.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error:
                  (error, stack) => Center(
                    child: OpenSansText(
                      'Error: $error',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppColors.red,
                    ),
                  ),
              data: (history) {
                if (history.items.isEmpty) {
                  return Center(
                    child: OpenSansText(
                      'No history available',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppColors.grey,
                    ),
                  );
                }

                // Get combined stepper items from both task details and history
                final steps = combineTaskDetailsAndHistory(
                  taskDetailsAsync.value,
                  history.items,
                );

                var createdBy = taskDetailsAsync.value?.createdBy ?? 'Unknown';

                return Padding(
                  padding: const EdgeInsets.only(
                    left: 12,
                    right: 12,
                    top: 2,
                    bottom: 2,
                  ),
                  child: CustomStepper(
                    steps: steps,
                    isCreatedByMeOnly:
                        createdBy == permissionChecker.GetPersonId(),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            // Remarks - Always show this
            const CustomRemarksField(),
            const SizedBox(height: 20),
            // Attachments - Always show this
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Heading with padding
                Padding(
                  padding: const EdgeInsets.only(
                    left: 12,
                    right: 12,
                    top: 2,
                    bottom: 2,
                  ),
                  child: const HeadingText(text: 'Attachments'),
                ),
                // AttachmentField with padding
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: AttachmentField(
                    uploadedFiles: uploadedFiles,
                    onFilesChanged: (p0) => {},
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Action Buttons - Always show this
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  // New method to handle comments section with loading state
  Widget _buildCommentsSection(
    AsyncValue<AbsenceItem?>? directAbsenceAsync,
    BuildContext context,
  ) {
    // Heading for the section
    Widget sectionTitle = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: DmSansText(
            'Comments',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Divider(color: AppColors.tcktdetailsdividerColor),
        const SizedBox(height: 8),
      ],
    );

    // If we don't have absence data yet, show a loading indicator
    if (directAbsenceAsync == null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          sectionTitle,
          const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 20.0),
              child: CircularProgressIndicator(),
            ),
          ),
          const Divider(color: AppColors.tcktdetailsdividerColor),
        ],
      );
    }

    // Show appropriate UI based on the state of the absence data
    return directAbsenceAsync.when(
      loading:
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              sectionTitle,
              const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 20.0),
                  child: CircularProgressIndicator(),
                ),
              ),
              const Divider(color: AppColors.tcktdetailsdividerColor),
            ],
          ),
      error:
          (error, stack) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              sectionTitle,
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20.0),
                  child: OpenSansText(
                    'Error loading comments: $error',
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: AppColors.red,
                  ),
                ),
              ),
              const Divider(color: AppColors.tcktdetailsdividerColor),
            ],
          ),
      data: (absenceItem) {
        if (absenceItem == null) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              sectionTitle,
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20.0),
                  child: OpenSansText(
                    'No comments available',
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: AppColors.grey,
                  ),
                ),
              ),
              const Divider(color: AppColors.tcktdetailsdividerColor),
            ],
          );
        }

        // We have data, show the full comments and attachments section based on task type
        return _buildCommentsAndAttachmentsSection(absenceItem, context);
      },
    );
  }

  Widget _buildCommentsAndAttachmentsSection(
    AbsenceItem? absenceItem,
    BuildContext context,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Base columns with common elements
    Column baseColumn = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(color: AppColors.tcktdetailsdividerColor),
        const SizedBox(height: 12),
      ],
    );

    // Check the task type and render accordingly
    switch (category) {
      case 'AbsencesApprovalsTask':
        // Existing implementation for leave approvals
        if (absenceItem == null) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Divider(color: AppColors.tcktdetailsdividerColor),
              const SizedBox(height: 12),
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20.0),
                  child: OpenSansText(
                    'No leave details available',
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: AppColors.grey,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              const Divider(color: AppColors.tcktdetailsdividerColor),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Divider(color: AppColors.tcktdetailsdividerColor),
            const SizedBox(height: 12),
            // Comment text
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: OpenSansText(
                    absenceItem.comment ?? '',
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.blackColor,
                    fontheight: 19.0 / 12.0,
                    softWrap: true,
                    overflow: TextOverflow.clip,
                  ),
                ),
              ],
            ),
            SizedBox(height: screenHeight * 0.02),
            // Display attachments if available
            if (absenceItem.absenceAttachments != null &&
                absenceItem.absenceAttachments!.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (
                    int i = 0;
                    i < absenceItem.absenceAttachments!.length;
                    i += 2
                  )
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        // First attachment (clickable)
                        Expanded(
                          child: AttachmentDownloadTile(
                            absenceUniqID:
                                absenceItem.personAbsenceEntryId?.toString() ??
                                '',
                            attachment: absenceItem.absenceAttachments![i],
                          ),
                        ),
                        // Second attachment (if exists, clickable)
                        if (i + 1 < absenceItem.absenceAttachments!.length)
                          const SizedBox(width: 12),
                        if (i + 1 < absenceItem.absenceAttachments!.length)
                          Expanded(
                            child: AttachmentDownloadTile(
                              absenceUniqID:
                                  absenceItem.personAbsenceEntryId
                                      ?.toString() ??
                                  '',
                              attachment:
                                  absenceItem.absenceAttachments![i + 1],
                            ),
                          ),
                      ],
                    ),
                ],
              ),
            const SizedBox(height: 12),
            const Divider(color: AppColors.tcktdetailsdividerColor),
          ],
        );

      case 'DocumentOfRecordApproval':
        // Not yet implemented - show placeholder
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Divider(color: AppColors.tcktdetailsdividerColor),
            const SizedBox(height: 12),
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 10.0),
                child: OpenSansText(
                  'Document details not available',
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppColors.grey,
                ),
              ),
            ),
            const SizedBox(height: 12),
            const Divider(color: AppColors.tcktdetailsdividerColor),
          ],
        );

      case 'Compensation':
        // Not yet implemented - show placeholder
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Divider(color: AppColors.tcktdetailsdividerColor),
            const SizedBox(height: 12),
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 10.0),
                child: OpenSansText(
                  'Claim details not available',
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppColors.grey,
                ),
              ),
            ),
            const SizedBox(height: 12),
            const Divider(color: AppColors.tcktdetailsdividerColor),
          ],
        );

      default:
        // Generic implementation for any other task types
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Divider(color: AppColors.tcktdetailsdividerColor),
            const SizedBox(height: 12),
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 10.0),
                child: OpenSansText(
                  'Details for ${category} not available',
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppColors.grey,
                ),
              ),
            ),
            const SizedBox(height: 12),
            const Divider(color: AppColors.tcktdetailsdividerColor),
          ],
        );
    }
  }

  /// Determines if a document should be shown for this history item
  bool _shouldShowDocument(dynamic historyItem) {
    if (historyItem == null || historyItem.state == null) return false;

    final state = historyItem.state.toString().toUpperCase();
    return state == 'APPROVED' || state == 'REJECTED';
  }

  // Method to map category to an appropriate title
  String _mapCategoryToTitle(String category, [String? taskTitle]) {
    switch (category) {
      case 'AbsencesApprovalsTask':
        return 'Leave Approval';
      case 'DocumentOfRecordApproval':
        // Extract document type from title
        if (taskTitle != null) {
          final regex = RegExp(r'\((.*?)\)');
          final match = regex.firstMatch(taskTitle);
          if (match != null) {
            var docType = match.group(1) ?? '';
            docType =
                docType
                    .replaceAll(
                      RegExp(
                        r'Request|Upload|,|Created for',
                        caseSensitive: false,
                      ),
                      '',
                    )
                    .trim();
            return docType.isNotEmpty ? docType : 'Document Request';
          }
        }
        return 'Document Request';
      case 'Compensation':
        return 'Claim Request';
      default:
        return category;
    }
  }

  Widget _buildHeader(TaskDetailsModel? taskDetails) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 2, bottom: 2),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DmSansText(
              _mapCategoryToTitle(category, taskDetails?.title),
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: AppColors.blackColor,
            ),
            SizedBox(height: 8),
            OpenSansText(
              'Request #${taskDetails?.number ?? ''}',
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppColors.lightBlack,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(
    TaskDetailsModel? taskDetails,
    AsyncValue<AbsenceItem?>? absenceDetails,
  ) {
    if (taskDetails == null) return const SizedBox.shrink();

    final String durationValue =
        absenceDetails?.asData?.value?.duration != null
            ? '${absenceDetails?.asData?.value?.duration} days'
            : taskDetails.approvalDuration != null
            ? '${taskDetails.approvalDuration} days'
            : 'N/A';

    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 2, bottom: 4),
      child: Column(
        children: [
          switch (category) {
            'AbsencesApprovalsTask' => Column(
              children: [
                _buildLeaveBalanceRow(taskDetails),
                InfoRow(
                  label: 'Submitted by',
                  value: taskDetails.createdBy ?? 'N/A',
                ),
                InfoRow(
                  label: 'Applied for',
                  value: taskDetails.createdDate ?? 'N/A',
                ),
                InfoRow(label: 'Duration', value: durationValue),
              ],
            ),
            'DocumentOfRecordApproval' => Column(
              children: [
                InfoRow(
                  label: 'Requested By',
                  value: taskDetails.createdBy ?? 'N/A',
                ),
                InfoRow(
                  label: 'Addressed To',
                  value:
                      taskDetails.assignees?.items?.firstOrNull?.firstName ??
                      'N/A',
                ),
              ],
            ),
            'Compensation' => Column(
              children: [
                InfoRow(
                  label: 'Submitted by',
                  value: taskDetails.createdBy ?? 'N/A',
                ),
                InfoRow(
                  label: 'Applied on',
                  value: taskDetails.createdDate ?? 'N/A',
                ),
                InfoRow(label: 'Claim Type', value: 'Medical'),
                InfoRow(label: 'Amount', value: 'AED 0.00'),
              ],
            ),
            _ => Column(
              children: [
                InfoRow(
                  label: 'Submitted by',
                  value: taskDetails.createdBy ?? 'N/A',
                ),
                InfoRow(
                  label: 'Applied on',
                  value: taskDetails.createdDate ?? 'N/A',
                ),
                InfoRow(
                  label: 'Addressed to',
                  value:
                      taskDetails.assignees?.items?.firstOrNull?.firstName ??
                      'N/A',
                ),
              ],
            ),
          },
        ],
      ),
    );
  }

  // New method to build the leave balance row with data from the provider
  Widget _buildLeaveBalanceRow(TaskDetailsModel taskDetails) {
    // Get the leave type from the task definition name or use a default
    final String leaveType = _mapCategoryToName(
      taskDetails.taskDefinitionName ?? 'Annual Leave',
    );

    // Use the leaveBalanceInfoProvider to fetch the balance
    return Consumer(
      builder: (context, ref, child) {
        final balanceAsync = ref.watch(leaveBalanceInfoProvider(leaveType));

        return balanceAsync.when(
          data: (balance) {
            String balanceText;

            if (balance == null || balance < 0) {
              balanceText = 'Not available';
            } else {
              balanceText = '$balance days';
            }

            return InfoRow(label: 'Leave Balance', value: balanceText);
          },
          loading: () => InfoRow(label: 'Leave Balance', value: 'Fetching...'),
          error:
              (err, stack) =>
                  InfoRow(label: 'Leave Balance', value: 'Not available'),
        );
      },
    );
  }

  String _mapCategoryToName(String category) {
    switch (category) {
      case 'AbsencesApprovalsTask':
        return 'Annual Leave';
      case 'Compensation':
        return 'Medical';
      case 'DocumentOfRecordApproval':
        return 'Letter';
      default:
        return category;
    }
  }

  Widget _buildActionButtons() {
    return Consumer(
      builder: (context, ref, child) {
        return Column(
          children: [
            CustomActionButton(
              text: 'Approve',
              backgroundColor: AppColors.viewColor,
              onPressed: () => _handleApproval(context, ref, true),
              textColor: AppColors.whiteColor,
            ),
            const SizedBox(height: 10),
            CustomActionButton(
              text: 'Reject',
              backgroundColor: AppColors.whiteColor,
              textColor: AppColors.viewColor,
              borderColor: AppColors.viewColor,
              onPressed: () => _handleApproval(context, ref, false),
            ),
          ],
        );
      },
    );
  }

  void _handleApproval(BuildContext context, WidgetRef ref, bool isApproved) {
    // Check if context is still valid
    if (!context.mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (dialogContext) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                OpenSansText(
                  'Processing your request...',
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ],
            ),
          ),
    );

    final remarkController = ref.read(remarkControllerProvider);

    final params = TaskApprovalParams(
      taskId: taskId.toString(),
      isApprove: isApproved,
      remarks: remarkController.text.trim(),
    );

    ref
        .read(taskApprovalProvider(params).future)
        .then((success) async {
          // Check if context is still valid before navigation
          if (!context.mounted) return;

          Navigator.pop(context); // close loading dialog

          if (success) {
            // Await dialog, keep bottom sheet open until dialog is closed
            await showApprovalStatusDialog(
              context: context,
              isApproved: isApproved,
            );

            // Check if context is still valid before navigation
            if (!context.mounted) return;

            // Now close bottom sheet after dialog dismissed
            Navigator.pop(context);

            // Refresh tasks list
            ref.invalidate(tasksFutureProvider);
          } else {
            if (!context.mounted) return;
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: OpenSansText(
                  'Failed to ${isApproved ? 'approve' : 'reject'} the request. Please try again.',
                  fontSize: 14,
                  color: AppColors.whiteColor,
                ),
                backgroundColor: AppColors.red,
              ),
            );
          }
        })
        .catchError((error) {
          // Check if context is still valid before navigation
          if (!context.mounted) return;

          Navigator.pop(context); // close loading dialog
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: OpenSansText(
                'Error: ${error.toString()}',
                fontSize: 14,
                color: AppColors.whiteColor,
              ),
              backgroundColor: AppColors.red,
            ),
          );
        });
  }

  Future<void> showApprovalStatusDialog({
    required BuildContext context,
    required bool isApproved,
  }) {
    SizeConfig.init(context);

    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          backgroundColor: Colors.transparent,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 16.h),
              Container(
                width: 353.w,
                height: 253.h,
                padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 20.w),
                decoration: BoxDecoration(
                  color: AppColors.whiteColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Container(
                      width: 74.w,
                      height: 74.h,
                      decoration: BoxDecoration(
                        color:
                            isApproved
                                ? AppColors.lightGreenShade
                                : AppColors.Orange,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isApproved ? Icons.check : Icons.close,
                        color: AppColors.viewColor,
                        size: 36.w,
                      ),
                    ),
                    SizedBox(height: 24.h),
                    DmSansText(
                      isApproved ? "Request approved" : "Request rejected",
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.blackColor,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 24.h),
                    SizedBox(
                      width: 313.w,
                      height: 56.h,
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(dialogContext),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2D3E50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: DmSansText(
                          'Close',
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: AppColors.whiteColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
