import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/parent/education/repository/educationRepository.dart';
import 'package:seawork/screens/parent/education/models/nurseryModel.dart';
import 'package:seawork/screens/parent/education/models/sectionModel.dart';

final educationRepositoryProvider = Provider<EducationRepository>(
  (ref) => EducationRepository(ref.read(dioProvider)),
);

final getNurseriesAutocompleteProvider =
    FutureProvider.autoDispose<List<Nursery>>((ref) async {
      final educationRepository = ref.watch(educationRepositoryProvider);
      final response = await educationRepository.getNurseriesAutocomplete();
      return response;
    });

final getAvailableSectionsWithRemainingSeatProvider =
    FutureProvider.family<List<Section>, ({int? studentId})>((
      ref,
      params,
    ) async {
      final educationRepository = ref.watch(educationRepositoryProvider);
      final response = await educationRepository
          .getAvailableSectionsWithRemainingSeat(studentId: params.studentId);
      return response;
    });
