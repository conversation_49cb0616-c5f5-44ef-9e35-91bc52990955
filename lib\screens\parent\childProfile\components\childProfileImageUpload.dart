import 'dart:io';
import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/utils/style/colors.dart';

class ChildProfileImageUpload extends StatelessWidget {
  final String gender;
  final File? profileImage;
  final VoidCallback onPickImage;

  const ChildProfileImageUpload({
    Key? key,
    required this.gender,
    required this.profileImage,
    required this.onPickImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final lowerGender = gender.trim().toLowerCase();
    final isFemale = lowerGender == 'female';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DMSans600Medium(14, 'Child profile image', AppColors.blackColor),
        const SizedBox(height: 10),
        GestureDetector(
          onTap: onPickImage,
          child: Row(
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppColors.lightGreyColor2,
                          width: 1,
                        ), // Add border here
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child:
                            profileImage != null
                                ? Image.file(profileImage!, fit: BoxFit.cover)
                                : CustomJpgImage(
                                  imageName:
                                      isFemale ? 'girl_avatar' : 'boy_avatar',
                                  boxFit: BoxFit.cover,
                                ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -12,
                    right: -12,
                    child: GestureDetector(
                      onTap: onPickImage,
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.editcolor,
                          shape: BoxShape.circle,
                        ),
                        padding: const EdgeInsets.all(6),
                        child: const CustomSvgImage(
                          imageName: 'edit_icon',
                          width: 18,
                          height: 18,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
