import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/screens/employee/absence/providers/absencesProviders.dart';
import 'package:seawork/screens/employee/approval/repository/approvalsRepository.dart';

import 'package:seawork/screens/employee/approval/models/taskdetailhistorymodel.dart';
import 'package:seawork/screens/employee/approval/models/taskdetailsmodel.dart';
import 'package:seawork/screens/employee/approval/models/approvalsModel.dart';

import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:seawork/screens/employee/absence/models/getAbsences.dart';
import 'package:seawork/data/module/networkModule.dart';

// Repository provider
final bpmRepositoryProvider = Provider(
  (ref) => BpmApiClient(ref.watch(dioProvider)),
);

// Selected tab provider
final selectedTabIndexProvider = StateProvider<int>((ref) => 0);

// Pagination state providers
final tasksOffsetProvider = StateProvider<int>((ref) => 0);
final tasksLimitProvider = StateProvider<int>((ref) => 500);
final hasMoreDataProvider = StateProvider<bool>((ref) => true);

// Task counts provider
final taskCountsFutureProvider = FutureProvider.autoDispose((ref) async {
  final repository = ref.read(bpmRepositoryProvider);
  return await repository.getTaskCounts();
});

// Task list state notifiers for each status
class TaskListNotifier extends StateNotifier<AsyncValue<List<TaskItem>>> {
  TaskListNotifier(this.ref, this.status) : super(const AsyncValue.loading()) {
    fetchData();
  }
  DateTime? _startDate;
  DateTime? _endDate;
  String searchQuery = '';
  final Ref ref;
  final String status;
  final List<TaskItem> _taskItems = [];
  bool _hasMore = true;
  String? _searchQuery;
  Future<void> filterByDate(DateTime? startDate, DateTime? endDate) async {
    _startDate = startDate;
    _endDate = endDate;
    // Reset pagination and reload data
    //  ref.read(tasksOffsetProvider.notifier).state = 0;
    // _taskItems.clear();
    // _hasMore = true;

    await fetchData();
  }

  Future<void> clearDateFilters() async {
    _startDate = null;
    _endDate = null;
    // Reset pagination and reload data
    ref.read(tasksOffsetProvider.notifier).state = 0;
    _taskItems.clear();
    _hasMore = true;

    await fetchData();
  }

  void clearSearch() {
    _searchQuery = null;
    fetchData();
  }

  Future<void> fetchData({String? searchQuery}) async {
    try {
      state = const AsyncValue.loading();
      final repository = ref.read(bpmRepositoryProvider);
      final offset = 0;
      final limit = ref.read(tasksLimitProvider);

      final response = await repository.getTasks(
        status: status,
        offset: offset,
        limit: limit,
        startDate: _startDate,
        endDate: _endDate,
        searchQuery: searchQuery,
      );

      _hasMore = response.hasMore;
      _taskItems.clear();
      _taskItems.addAll(response.items);

      state = AsyncValue.data(_taskItems);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> fetchPaginatedData({
    required int offset,
    bool prepend = false,
  }) async {
    if (!_hasMore && !prepend) return;

    try {
      final repository = ref.read(bpmRepositoryProvider);
      final limit = ref.read(tasksLimitProvider);

      final response = await repository.getTasks(
        status: status,
        offset: offset,
        limit: limit,
        startDate: _startDate,
        endDate: _endDate,
        searchQuery: searchQuery,
      );

      // Update hasMore flag
      _hasMore = response.hasMore;

      if (response.items.isNotEmpty) {
        // Filter out duplicates
        final newItems =
            response.items
                .where(
                  (newItem) =>
                      !_taskItems.any(
                        (existing) => existing.number == newItem.number,
                      ),
                )
                .toList();

        if (prepend) {
          _taskItems.insertAll(0, newItems);
        } else {
          _taskItems.addAll(newItems);
        }
      }

      state = AsyncValue.data(_taskItems);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  bool get hasMore => _hasMore;
  List<TaskItem> get items => _taskItems;
}

// Create task list notifier providers for each status
final pendingTasksNotifierProvider =
    StateNotifierProvider<TaskListNotifier, AsyncValue<List<TaskItem>>>(
      (ref) => TaskListNotifier(ref, "ASSIGNED"),
    );

final approvedTasksNotifierProvider =
    StateNotifierProvider<TaskListNotifier, AsyncValue<List<TaskItem>>>(
      (ref) => TaskListNotifier(ref, "COMPLETED"),
    );

final rejectedTasksNotifierProvider =
    StateNotifierProvider<TaskListNotifier, AsyncValue<List<TaskItem>>>(
      (ref) => TaskListNotifier(ref, "SUSPENDED"),
    );

final withdrawnTasksNotifierProvider =
    StateNotifierProvider<TaskListNotifier, AsyncValue<List<TaskItem>>>(
      (ref) => TaskListNotifier(ref, "WITHDRAWN"),
    );

// Legacy providers for backward compatibility
final pendingTasksProvider = FutureProvider.autoDispose((ref) async {
  final tasksAsync = ref.watch(pendingTasksNotifierProvider);
  return tasksAsync.when(
    data:
        (tasks) => TaskListResponse(
          count: tasks.length,
          hasMore: ref.read(pendingTasksNotifierProvider.notifier).hasMore,
          items: tasks,
        ),
    loading: () => TaskListResponse(count: 0, hasMore: false, items: []),
    error: (_, __) => TaskListResponse(count: 0, hasMore: false, items: []),
  );
});

final approvedTasksProvider = FutureProvider.autoDispose((ref) async {
  final tasksAsync = ref.watch(approvedTasksNotifierProvider);
  return tasksAsync.when(
    data:
        (tasks) => TaskListResponse(
          count: tasks.length,
          hasMore: ref.read(approvedTasksNotifierProvider.notifier).hasMore,
          items: tasks,
        ),
    loading: () => TaskListResponse(count: 0, hasMore: false, items: []),
    error: (_, __) => TaskListResponse(count: 0, hasMore: false, items: []),
  );
});

final rejectedTasksProvider = FutureProvider.autoDispose((ref) async {
  final tasksAsync = ref.watch(rejectedTasksNotifierProvider);
  return tasksAsync.when(
    data:
        (tasks) => TaskListResponse(
          count: tasks.length,
          hasMore: ref.read(rejectedTasksNotifierProvider.notifier).hasMore,
          items: tasks,
        ),
    loading: () => TaskListResponse(count: 0, hasMore: false, items: []),
    error: (_, __) => TaskListResponse(count: 0, hasMore: false, items: []),
  );
});

final withdrawnTasksProvider = FutureProvider.autoDispose((ref) async {
  final tasksAsync = ref.watch(withdrawnTasksNotifierProvider);
  return tasksAsync.when(
    data:
        (tasks) => TaskListResponse(
          count: tasks.length,
          hasMore: ref.read(withdrawnTasksNotifierProvider.notifier).hasMore,
          items: tasks,
        ),
    loading: () => TaskListResponse(count: 0, hasMore: false, items: []),
    error: (_, __) => TaskListResponse(count: 0, hasMore: false, items: []),
  );
});

// State providers to store the items for each status (for backward compatibility)
final pendingTasksListProvider = StateProvider<List<TaskItem>>((ref) => []);
final approvedTasksListProvider = StateProvider<List<TaskItem>>((ref) => []);
final rejectedTasksListProvider = StateProvider<List<TaskItem>>((ref) => []);
final withdrawnTasksListProvider = StateProvider<List<TaskItem>>((ref) => []);

// All tasks provider (for backward compatibility)
final tasksFutureProvider = FutureProvider.autoDispose((ref) async {
  final selectedTabIndex = ref.watch(selectedTabIndexProvider);

  switch (selectedTabIndex) {
    case 0:
      return await ref.watch(pendingTasksProvider.future);
    case 1:
      return await ref.watch(approvedTasksProvider.future);
    case 2:
      return await ref.watch(rejectedTasksProvider.future);
    case 3:
      return await ref.watch(withdrawnTasksProvider.future);
    default:
      return TaskListResponse(count: 0, hasMore: false, items: []);
  }
});

final taskDetailsProvider = FutureProvider.autoDispose
    .family<TaskDetailsModel, String>((ref, taskId) async {
      try {
        final repository = ref.read(bpmRepositoryProvider);
        print('Fetching task details for ID: $taskId');
        return await repository.getTaskDetails(taskId);
      } catch (e) {
        print('Error in taskDetailsProvider: $e');
        rethrow; // Rethrow to allow the UI to handle the error
      }
    });

final taskHistoryProvider = FutureProvider.autoDispose
    .family<TaskHistoryResponse, String>((ref, taskId) async {
      try {
        final repository = ref.read(bpmRepositoryProvider);
        print('Fetching task history for ID: $taskId');
        return await repository.getTaskHistory(taskId);
      } catch (e) {
        print('Error in taskHistoryProvider: $e');
        rethrow; // Rethrow to allow the UI to handle the error
      }
    });

// Provider for handling task approval/rejection operations
final taskApprovalProvider = FutureProvider.autoDispose
    .family<bool, TaskApprovalParams>((ref, params) async {
      final repository = ref.read(bpmRepositoryProvider);
      return await repository.approveOrRejectTask(
        taskId: params.taskId,
        isApprove: params.isApprove,
        remarks: params.remarks,
      );
    });

// Class to hold the approval/rejection parameters
class TaskApprovalParams {
  final String taskId;
  final bool isApprove;
  final String remarks;

  TaskApprovalParams({
    required this.taskId,
    required this.isApprove,
    required this.remarks,
  });
}

// Provider for leave balance information
final leaveBalanceInfoProvider = FutureProvider.family<double?, String>((
  ref,
  leaveType,
) async {
  try {
    final repository = ref.read(leaveTypesRepositoryProvider);
    // Backend will automatically use the current user's PersonId from token
    final planBalances = await repository.getPlanBalances(true);

    if (planBalances.isEmpty) return -1;

    // Check if any plan matches the selected leave type
    final hasMatchingPlan = planBalances.any(
      (plan) => plan.planName == leaveType,
    );

    if (!hasMatchingPlan) return 0;

    // Get the matching plan
    final matchingPlan = planBalances.firstWhere(
      (plan) => plan.planName == leaveType,
    );

    final selectedBalance = matchingPlan.balanceAsOfBalanceCalculationDate;
    final absenceBalance =
        selectedBalance != null ? selectedBalance.ceilToDouble() : -1;

    return absenceBalance.toDouble();
  } catch (e) {
    print('Error fetching leave balance: $e');
    return -1.0;
  }
});

// Providers moved from ApprovalBottomSheet (for use in approval UI)

// Provider for remark text controller
final remarkControllerProvider =
    StateProvider.autoDispose<TextEditingController>(
      (ref) => TextEditingController(),
    );

// Provider for absence details
final absenceDetailsProvider = FutureProvider.family.autoDispose<
  AbsenceItem?,
  String
>((ref, absencesUniqID) async {
  try {
    final repository = ref.read(leaveTypesRepositoryProvider);
    final attachmentIds = await repository.getAbsenceByUniqID(absencesUniqID);
    // The repository method updates the repository's internal state and returns attachment IDs
    // We need to directly access the API response data to get the absence details
    final absencesList = repository.fullAbsencesList;
    // Find the absence with matching ID
    if (absencesList.isNotEmpty) {
      final foundAbsence = absencesList.firstWhere(
        (absence) => absence.personAbsenceEntryId?.toString() == absencesUniqID,
        orElse: () => AbsenceItem(),
      );
      return foundAbsence;
    }
    return null;
  } catch (e) {
    return null;
  }
});

// Provider that directly fetches absence data
final directAbsenceProvider = FutureProvider.family.autoDispose<
  AbsenceItem?,
  String
>((ref, absencesUniqID) async {
  try {
    // Create a Dio instance
    final dio = Dio();
    // Make a direct API call to get the absence details
    final response = await dio.get(
      '${baseUrlEMSProvider}/absences/$absencesUniqID?expand=absenceAttachments',
    );
    if (response.statusCode == 200 && response.data != null) {
      // Parse the response data into an AbsenceItem
      final absenceItem = AbsenceItem.fromJson(response.data);
      return absenceItem;
    }
    return null;
  } catch (e) {
    return null;
  }
});
final approvalDateRangeProvider = StateProvider<Map<String, DateTime?>?>(
  (ref) => null,
);
