import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:seawork/screens/employee/paySlip/models/payslipModel.dart';
import 'package:seawork/utils/downloadUtils.dart';
import 'package:universal_html/html.dart' as web;

import 'package:seawork/data/module/networkModule.dart';

class PayslipRepository {
  final Dio _dio;

  PayslipRepository(this._dio);

  Future<List<PaySlip>> fetchPayslips({int limit = 25, int offset = 0}) async {
    try {
      final response = await _dio.get(
        '${baseUrlEMSProvider}/payslips',
        queryParameters: {
          'fields':
              'DocumentsOfRecordId,Amount,DefaultCurrencyCode,PaymentDate,RelActionId,CreationDate,CurrencyCode,WeekDay,PeriodEndDate,PeriodStartDate',
          'orderBy': 'CreationDate:desc',
          'totalResults': 'true',
          'limit': limit,
          'offset': offset,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final List<dynamic> items = data['items'];
        return items.map((item) => PaySlip.fromJson(item)).toList();
      } else {
        throw Exception('Failed to load payslips: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching payslips: $e');
      }
      throw Exception('Failed to load payslips: $e');
    }
  }

  void triggerDownload(List<int> bytes, String filename) {
    final blob = web.Blob([Uint8List.fromList(bytes)], 'application/pdf');
    final url = web.Url.createObjectUrlFromBlob(blob);
    final anchor =
        web.AnchorElement(href: url)
          ..download = filename
          ..target = '_blank'
          ..style.display = 'none';

    web.document.body!.append(anchor);
    anchor.click();

    Future.delayed(Duration(milliseconds: 500), () {
      anchor.remove();
      web.Url.revokeObjectUrl(url);
    });
  }

  Future<String> downloadPayslipAttachment(int documentsOfRecordId, {required dynamic context}) async {
    try {
      // Step 1: Get document details
      final documentResponse = await _dio.get(
        '${baseUrlEMSProvider}/payslips/$documentsOfRecordId',
        queryParameters: {
          'expand': 'documents',
        },
      );

      // Extract documentsUniqID
      final documents = documentResponse.data['documents'] as List;
      if (documents.isEmpty) throw Exception('No documents found');

      final selfLink =
          (documents.first['links'] as List).firstWhere(
                (link) => link['rel'] == 'self',
              )['href']
              as String;
      final documentsUniqID = selfLink.split('/').last;
      await downloadFileWithDio(
        context: context, // <-- pass context here
        dio: _dio,
        url:
            '${baseUrlEMSProvider}/payslips/$documentsOfRecordId/child/documents/$documentsUniqID/enclosure/fileContents',
        fileName: 'payslip_$documentsOfRecordId.pdf',
      );
      return 'payslip_$documentsOfRecordId.pdf';
    } catch (e) {
      if (kDebugMode) print('Error downloading payslip: $e');
      throw Exception('Failed to download payslip: $e');
    }
  }
}
