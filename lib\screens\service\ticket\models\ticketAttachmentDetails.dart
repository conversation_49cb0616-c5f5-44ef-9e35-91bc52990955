import 'package:json_annotation/json_annotation.dart';

part 'ticketAttachmentDetails.g.dart';

@JsonSerializable()
class AttachmentDetails {
  final int ticket_id;
  final int record_count;
  final List<Attachment> attachments;
  final List<dynamic> folders;

  AttachmentDetails({
    required this.ticket_id,
    required this.record_count,
    required this.attachments,
    required this.folders,
  });

  factory AttachmentDetails.fromJson(Map<String, dynamic> json) {
    return AttachmentDetails(
      ticket_id: json['ticket_id'] as int,
      record_count: json['record_count'] as int,
      attachments:
          (json['attachments'] as List)
              .map((e) => Attachment.fromJson(e as Map<String, dynamic>))
              .toList(),
      folders: json['folders'] as List<dynamic>,
    );
  }
  Map<String, dynamic> toJson() => _$AttachmentDetailsToJson(this);
}

@JsonSerializable()
class Attachment {
  final int id;
  final String filename;
  final DateTime datecreated;
  final int filesize;
  final bool isimage;
  final int faultid;

  Attachment({
    required this.id,
    required this.filename,
    required this.datecreated,
    required this.filesize,
    required this.isimage,
    required this.faultid,
  });

  factory Attachment.fromJson(Map<String, dynamic> json) =>
      _$AttachmentFromJson(json);
  Map<String, dynamic> toJson() => _$AttachmentToJson(this);
}
