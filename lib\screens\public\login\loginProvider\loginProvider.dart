import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/screens/public/login/loginRepository/loginRepository.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/constants/constantTexts.dart';

// Provider for LoginRepository
final loginRepositoryProvider = Provider<LoginRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return LoginRepository(baseUrl: baseUrlPMSProvider.toString(), dio: dio);
});

// State for OTP Generation
final generateOtpProvider = StateNotifierProvider<
  GenerateOtpNotifier,
  AsyncValue<Map<String, dynamic>>
>((ref) {
  final repo = ref.watch(loginRepositoryProvider);
  return GenerateOtpNotifier(repo);
});

class GenerateOtpNotifier
    extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final LoginRepository repository;
  GenerateOtpNotifier(this.repository) : super(const AsyncValue.data({}));

  Future<void> generateOtp(String phoneNumber) async {
    print('GenerateOtpNotifier: Starting OTP generation for: $phoneNumber');

    if (phoneNumber.isEmpty) {
      print('GenerateOtpNotifier: Phone number is empty');
      state = AsyncValue.error(
        ConstantTexts.phoneNumberCannotBeEmpty,
        StackTrace.current,
      );
      Fluttertoast.showToast(msg: ConstantTexts.pleaseEnterPhoneNumber);
      return;
    }

    // --- For production, use the following cleaning and validation logic ---
    // var cleanPhoneNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    // print('GenerateOtpNotifier: Cleaned phone number: $cleanPhoneNumber');
    // if (cleanPhoneNumber.length == 10 && cleanPhoneNumber.startsWith('0')) {
    //   cleanPhoneNumber = cleanPhoneNumber.substring(1);
    //   print('GenerateOtpNotifier: Removed leading zero, now: $cleanPhoneNumber');
    // }
    // if (cleanPhoneNumber.length != 9) {
    //   print('GenerateOtpNotifier: Invalid phone number length: ${cleanPhoneNumber.length}');
    //   state = AsyncValue.error('Invalid phone number length', StackTrace.current);
    //   Fluttertoast.showToast(msg: ConstantTexts.pleaseEnterValidNineDigitUaePhone);
    //   return;
    // }

    // --- For testing, allow any phone number ---
    final cleanPhoneNumber = phoneNumber;

    print('GenerateOtpNotifier: Setting loading state');
    state = const AsyncValue.loading();

    try {
      print('GenerateOtpNotifier: Calling repository.generateOtp');
      final result = await repository.generateOtp(cleanPhoneNumber);
      print('GenerateOtpNotifier: Got result from repository: $result');
      state = AsyncValue.data(result);
    } catch (e, st) {
      print('GenerateOtpNotifier: Error occurred: $e');
      print('GenerateOtpNotifier: Stack trace: $st');
      state = AsyncValue.error(e, st);
      Fluttertoast.showToast(
        msg: '${ConstantTexts.failedToGenerateOtpWithError}${e.toString()}',
        toastLength: Toast.LENGTH_LONG,
      );
    }
  }
}

// State for OTP Validation
final validateOtpProvider = StateNotifierProvider<
  ValidateOtpNotifier,
  AsyncValue<Map<String, dynamic>>
>((ref) {
  final repo = ref.watch(loginRepositoryProvider);
  return ValidateOtpNotifier(repo);
});

class ValidateOtpNotifier
    extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final LoginRepository repository;
  ValidateOtpNotifier(this.repository) : super(const AsyncValue.data({}));

  Future<void> validateOtp(String phoneNumber, String otp) async {
    // Commenting out validation for testing
    // if (phoneNumber.isEmpty || otp.isEmpty) {
    //   state = AsyncValue.error(
    //       ConstantTexts.phoneNumberCannotBeEmpty, StackTrace.current);
    //   Fluttertoast.showToast(msg: ConstantTexts.pleaseEnterBothPhoneAndOtp);
    //   return;
    // }

    // // Remove any spaces or special characters
    // final cleanPhoneNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // // Validate phone number length (excluding country code)
    // if (cleanPhoneNumber.length < 9 || cleanPhoneNumber.length > 9) {
    //   state =
    //       AsyncValue.error(ConstantTexts.invalidPhoneNumberLength, StackTrace.current);
    //   Fluttertoast.showToast(msg: ConstantTexts.pleaseEnterValidNineDigitPhone);
    //   return;
    // }

    // Using phone number directly without validation for testing
    final cleanPhoneNumber = phoneNumber;

    state = const AsyncValue.loading();
    try {
      final result = await repository.validateOtp(cleanPhoneNumber, otp);
      state = AsyncValue.data(result);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      Fluttertoast.showToast(
        msg: '${ConstantTexts.failedToValidateOtpWithError}${e.toString()}',
      );
    }
  }
}
