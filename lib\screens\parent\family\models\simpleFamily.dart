import 'package:json_annotation/json_annotation.dart';

part 'simpleFamily.g.dart';

@JsonSerializable()
class SimpleFamily {
  @<PERSON><PERSON><PERSON><PERSON>(name: "<PERSON>N<PERSON>")
  final String nickName;
  @<PERSON><PERSON><PERSON><PERSON>(name: "Id")
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: "SpouseId")
  final dynamic spouseId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "RealtionshipStatusId")
  final int realtionshipStatusId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "SpouseNameInArabic")
  final String? spouseNameInArabic;
  @<PERSON><PERSON><PERSON><PERSON>(name: "SpouseName")
  final String? spouseName;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ProfileDocumentUpload")
  final String? profileDocumentUpload;
  @<PERSON><PERSON><PERSON><PERSON>(name: "kidsIds")
  final List<int> kidsIds;

  SimpleFamily({
    required this.nickName,
    required this.id,
    this.spouseId,
    required this.realtionshipStatusId,
    this.spouseNameInArabic,
    this.spouseName,
    this.profileDocumentUpload,
    required this.kidsIds,
  });

  SimpleFamily copyWith({
    String? nickName,
    int? id,
    String? spouseId,
    int? realtionshipStatusId,
    String? spouseNameInArabic,
    String? spouseName,
    String? profileDocumentUpload,
    List<int>? kidsIds,
  }) => SimpleFamily(
    nickName: nickName ?? this.nickName,
    id: id ?? this.id,
    spouseId: spouseId ?? this.spouseId,
    realtionshipStatusId: realtionshipStatusId ?? this.realtionshipStatusId,
    spouseNameInArabic: spouseNameInArabic ?? this.spouseNameInArabic,
    spouseName: spouseName ?? this.spouseName,
    profileDocumentUpload: profileDocumentUpload ?? this.profileDocumentUpload,
    kidsIds: kidsIds ?? this.kidsIds,
  );

  factory SimpleFamily.fromJson(Map<String, dynamic> json) =>
      _$SimpleFamilyFromJson(json);

  Map<String, dynamic> toJson() => _$SimpleFamilyToJson(this);
}
