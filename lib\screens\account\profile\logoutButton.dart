import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/spacing/padding.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:go_router/go_router.dart';

class LogoutButton extends StatelessWidget {
  const LogoutButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding16x8(
      Container(
        height: 56,
        child: ElevatedButton(
          onPressed: () async {
            await PreferencesUtils.remove(PreferencesUtils.SESSION_TOKEN);
            await PreferencesUtils.remove(PreferencesUtils.USER);
            await PreferencesUtils.remove(PreferencesUtils.USER_PROFILES);
            final container = ProviderScope.containerOf(context, listen: false);
            container.read(bottomNavBarProvider.notifier).state = 0;
            context.go('/login');
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.secondaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: AppColors.viewColor),
            ),
            elevation: 0,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomSvgImage(imageName: 'ic_logout'),
              Padding2x2(SizedBox()),
              DMSans600Large(14, 'Logout', AppColors.viewColor),
            ],
          ),
        ),
      ),
    );
  }
}
