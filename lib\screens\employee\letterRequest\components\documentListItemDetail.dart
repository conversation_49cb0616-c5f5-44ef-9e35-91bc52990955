import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/permission/permissions.dart';

import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customStepper.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/employee/approval/components/customActionButton.dart';
import 'package:seawork/screens/employee/letterRequest/models/document_record_attachment_response_model.dart';
import 'package:seawork/screens/employee/letterRequest/models/documents_record_model.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_types_provider.dart';
import 'package:seawork/utils/style/colors.dart';

class DocumentListItemDetail extends ConsumerWidget {
  final DocumentRecord documentListItem;
  final BuildContext scaffoldContext; 
  DocumentListItemDetail(this.documentListItem, this.scaffoldContext, {super.key});
  final PermissionChecker permissionChecker = PermissionChecker();
  String _toSentenceCase(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  String? extractFileContentsPath(AttachmentItem item) {
    final links = item.links;
    for (final link in links) {
      if (link.rel == 'enclosure' && link.name == 'FileContents') {
        final fullUrl = link.href;
        final uri = Uri.parse(fullUrl);
        return uri.pathSegments
            .skipWhile((segment) => segment != 'documentRecords')
            .join('/');
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final taskDetailsAsync = ref.watch(taskDetailsProvider(taskId.toString()));
    // final taskHistoryAsync = ref.watch(taskHistoryProvider(taskId.toString()));
    final taskNumber =
        documentListItem.bannerOverrideMessages?.isNotEmpty == true
            ? documentListItem.bannerOverrideMessages!.first.taskNumber
            : null;

    final documentsOfRecordId = documentListItem.DocumentsOfRecordId;
    bool isAttachment = documentListItem.AttachmentsCount > 0;

    final documentsAsync =
        (taskNumber != null)
            ? ref.watch(GetDocumentRecordStatusHistoryProvider(taskNumber))
            : const AsyncValue.loading();

    final documentsAttachmentAsync =
        isAttachment
            ? ref.watch(
              GetDocumentRecordAttachmentsProvider(documentsOfRecordId),
            )
            : const AsyncValue.loading();

    print(documentListItem.DocumentType);
    final List<FileModel> uploadedFiles = [];
    final updatedDocument = TempTaskDetails(
      createdBy: documentListItem.CreatedBy ?? 'Unknown',
      createdDate: documentListItem.CreationDate ?? '',
    );
    final steps = combineTaskDetailsAndHistory(
      updatedDocument,
      documentsAsync.maybeWhen(data: (data) => data.items, orElse: () => []),
    );

    var createdBy = documentListItem.CreatedBy ?? 'Unknown';
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: ListView(
        shrinkWrap: true,
        children: [
          Center(child: CustomSvgImage(imageName: 'Rectangle12231')),
          const SizedBox(height: 16),

          // Header Section
          _buildHeader(_toSentenceCase(documentListItem.DocumentType)),
          const SizedBox(height: 20),

          // Information Rows
          _buildInfoSection(documentListItem),
          const SizedBox(height: 20),
          const Divider(color: AppColors.tcktdetailsdividerColor),
          if (documentListItem.attachments!.isNotEmpty) ...[
            const Text(
              'Attachment added in above type please accept those request Thank you.',
              style: TextStyle(fontWeight: FontWeight.w400, fontSize: 12),
            ),
            const SizedBox(height: 20),
            _buildAttachmentsSection(
              documentsAttachmentAsync.maybeWhen(
                data: (data) => data.items,
                orElse: () => [],
              ),
              ref,
              context,
            ),
            const SizedBox(height: 20),
            const Divider(color: AppColors.tcktdetailsdividerColor),
          ],
          const SizedBox(height: 16),

          // Status section
          CustomStepper(
            steps: steps,
            isCreatedByMeOnly: createdBy == permissionChecker.GetPersonId(),
          ),

          // Action Buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// Determines if a document should be shown for this history item
  bool _shouldShowDocument(dynamic historyItem) {
    if (historyItem == null || historyItem.state == null) return false;

    final state = historyItem.state.toString().toUpperCase();
    return state == 'APPROVED' || state == 'REJECTED';
  }

  Widget _buildHeader(String heading) {
    return Text(
      heading,
      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildInfoSection(DocumentRecord? documentListItem) {
    return Column(
      children: [
        _InfoRow(
          label: 'Submitted by',
          value: documentListItem!.CreatedBy ?? 'N/A',
        ),
        _InfoRow(
          label: 'Addressed to',
          value:
              documentListItem.documentRecordsDFF != null &&
                      documentListItem.documentRecordsDFF!.isNotEmpty &&
                      documentListItem.documentRecordsDFF![0].containsKey(
                        'addressTo',
                      )
                  ? documentListItem.documentRecordsDFF![0]['addressTo'] ?? ''
                  : 'Not available',
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return CustomActionButton(
      text: 'Withdraw',
      backgroundColor: const Color(0xFF395062),
      onPressed: () => _handleApproval(true),
    );
  }

  void _handleApproval(bool isApproved) {
    // Implement approval logic
  }

  Widget _buildAttachmentsSection(
    List<AttachmentItem> attachments,
    WidgetRef ref,
    BuildContext context,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              for (final attachment in attachments)
                Padding(
                  padding: const EdgeInsets.only(right: 12.0),
                  child: _buildAttachmentCard(attachment, ref, context),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAttachmentCard(AttachmentItem attachment, WidgetRef ref, BuildContext context) {
    final isLoading = ref.watch(downloadLoadingProvider);

    final fileContentsPath = extractFileContentsPath(attachment);
    if (fileContentsPath == null) {
      return const SizedBox.shrink();
    }
    final url = baseUrlEMSProvider + "/" + fileContentsPath;
    return GestureDetector(
      onTap:
          isLoading
              ? null
              : () => _handleDownload(url, attachment.fileName!, ref, context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFCDD6DC)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Image.asset('assets/images/pdfincoming.png', width: 24, height: 24),
            const SizedBox(width: 8),
            SizedBox(
              width: 100,
              child: Text(
                attachment.fileName!,
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF062540),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(width: 8),
            isLoading
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
                : Image.asset(
                  'assets/images/download.jpg',
                  width: 20,
                  height: 20,
                ),
          ],
        ),
      ),
    );
  }

  void _handleDownload(String url, String filename, WidgetRef ref, BuildContext context) async {
    final download = ref.read(downloadAttachmentProvider);
    await download(url, 'attachment_$filename', context);
  }

  String formatCreationDate(String isoDateString) {
    try {
      final date = DateTime.parse(isoDateString);
      final day = date.day;
      final suffix = _getDaySuffix(day);
      final formattedDate = DateFormat("dd'$suffix' MMMM, yyyy").format(date);
      return formattedDate;
    } catch (e) {
      return 'Invalid date';
    }
  }

  String _getDaySuffix(int day) {
    if (day >= 11 && day <= 13) {
      return 'th';
    }
    switch (day % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }
}

class _InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const _InfoRow({required this.label, required this.value});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.blackColor,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w400,
              color: AppColors.lightGreyColor,
            ),
          ),
        ],
      ),
    );
  }
}

class TempTaskDetails {
  final String createdBy;
  final String createdDate;

  TempTaskDetails({required this.createdBy, required this.createdDate});
}
