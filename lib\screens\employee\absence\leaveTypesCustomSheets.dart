import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/bottomSheetProvider/bottomSheetProvider.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/permission/permissions.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/screens/employee/absence/form.dart';
import 'package:seawork/screens/employee/absence/models/getAbsences.dart';
import 'package:seawork/screens/employee/absence/providers/absenceListNotifier.dart';
import 'package:seawork/screens/employee/absence/providers/absencesNotifier.dart';
import 'package:seawork/screens/employee/approval/models/taskdetailsmodel.dart';
import 'package:seawork/screens/employee/approval/providers/approvalsProvider.dart';
import 'package:seawork/screens/employee/letterRequest/models/letterDummyModel.dart';
import 'package:seawork/screens/employee/task/models/taskModel.dart';
import 'package:seawork/screens/employee/absence/providers/absencesProviders.dart';
import 'package:seawork/screens/employee/absence/providers/leaveRequestProviders.dart';
import 'package:seawork/screens/employee/absence/applyLeave.dart';

import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customInfoRow.dart';
import 'package:seawork/components/widget/dateFormatter.dart';

import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/utils/util.dart';

import '../../../components/widget/customStepper.dart';

class CustomBottomSheet extends ConsumerStatefulWidget {
  final String status;
  final dynamic SelectedTabIndex;
  final String? iconClicked;
  bool? assigntomePending;
  final String? timelineStatus;
  String? date1;
  String? date2;
  String? remarks;
  LetterRequest? LeaveData;
  AbsenceItem? absence;
  TaskItem? task;
  dynamic leaveTypes;
  bool showStepper = false; // Initially hide stepper
  final PermissionChecker permissionChecker = PermissionChecker();
  CustomBottomSheet({
    Key? key,
    required this.status,
    this.absence,
    this.timelineStatus,
    this.SelectedTabIndex,
    this.date1,
    this.date2,
    this.remarks,
    this.LeaveData,
    this.assigntomePending,
    this.iconClicked,
    this.task,
    this.leaveTypes,
  }) : super(key: key);
  @override
  ConsumerState<CustomBottomSheet> createState() => _CustomBottomSheetState();
}

class _CustomBottomSheetState extends ConsumerState<CustomBottomSheet> {
  bool initialized = false;
  Map<String, bool> _isLoading = {};
  final PermissionChecker _permissionChecker = PermissionChecker();

  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      final taskStatus = switch (widget.status.toLowerCase()) {
        'pending' => 'ASSIGNED',
        'approved' || 'rejected' => 'COMPLETED',
        'withdrawn' => 'WITHDRAWN',
        _ => null,
      };

      if (taskStatus != null) {
        ref.read(taskStatusProvider.notifier).state = taskStatus;
      }
      ref.read(absenceIdProvider.notifier).state =
          widget.absence?.personAbsenceEntryId ?? 0;

      setState(() {
        initialized = true;
      });
    });
  }

  void refetchTasks(WidgetRef ref) {
    ref.invalidate(taskDetailsProvider); // This will refetch the task list
  }

  @override
  Widget build(BuildContext context) {
    SingleTask? task;
    TaskDetailsModel? filteredTask;
    if (!initialized) {
      return const Center(child: CircularProgressIndicator());
    }
    final taskDetailsAsync = ref.watch(taskItemsProvider);

    taskDetailsAsync.when(
      data: (taskDetails) {
        if (taskDetails != null) {
          filteredTask = taskDetails;
        } else {
          filteredTask = null;
        }
      },
      loading: () {
        // show loading or keep filteredTask as null
      },
      error: (error, stack) {
        // optionally log error
        filteredTask = null;
      },
    );

    final taskNumber = taskDetailsAsync.maybeWhen(
      data: (task) => task?.number?.toString() ?? '',
      orElse: () => '',
    );

    final taskHistoryAsync =
        taskNumber.isNotEmpty
            ? ref.watch(taskHistoryProvider(taskNumber))
            : const AsyncValue.loading();

    final isLoadingAsync = ref.watch(taskLoadingProvider);
    Future<void> WithdrawRequest(WidgetRef ref) async {
      ref.read(isSubmittingProvider.notifier).state = true; // Start Loading

      // Merge attachments into updated fields
      Map<String, dynamic> updatedFields = {"absenceStatusCd": "ORA_WITHDRAWN"};

      try {
        final params = (
          absenceRequest: updatedFields,
          absenceUniqID: widget.absence?.personAbsenceEntryId.toString() ?? '',
        );

        // 🔹 Step 2: Update Absence Record AFTER Upload
        await ref.read(leaveRequestupdateProvider(params).future);
        Fluttertoast.showToast(
          msg: "Withdrawn Successfully",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 3,
          backgroundColor: AppColors.darkGrey,
          textColor: AppColors.whiteColor,
          fontSize: 15.0,
        );

        // ScaffoldMessenger.of(context).showSnackBar(
        //   const SnackBar(
        //       content: Center(child: Text("Withdrawn Successfully"))),
        // );
      } catch (e) {
      } finally {
        ref.read(awaitingListNotifierProvider.notifier).fetchAllAbsences();

        ref.read(isSubmittingProvider.notifier).state = false; // Stop Loading
      }
    }

    // if (taskDetailsAsync.value != null && taskDetailsAsync.value!.isNotEmpty) {
    //   for (var task in taskDetailsAsync.value!) {
    //   }

    //   final taskNumber = getFilteredTaskNumber(
    //       taskDetailsAsync.value ?? [], absence?.personAbsenceEntryId);

    //   if (taskNumber.isNotEmpty) {
    //     final taskAsync = ref.watch(taskProvider(taskNumber.toString()));
    //     task = taskAsync.value ?? SingleTask();
    //   }
    // } else {
    // }

    final absencesUniqID = widget.absence?.personAbsenceEntryId ?? '';

    final attachmentIDS = ref.watch(
      getAbsenceByUniqIDProvider(absencesUniqID.toString()),
    );
    dynamic AbsenceAttachments;

    Widget buildAttachmentContainer(
      attachment,
      double screenWidth,
      double screenHeight,
      bool isFirstContainer,
      String attachmentId,
      int? absencesUniqID,
    ) {
      return Padding(
        padding: EdgeInsets.only(top: screenHeight * 0.01),
        child: GestureDetector(
          onTap: () async {
            // Fetch base64 data

            String _getMimeTypeFromFileName(String fileName) {
              final ext = fileName.split('.').last.toLowerCase();
              switch (ext) {
                case 'pdf':
                  return 'application/pdf';
                case 'png':
                  return 'image/png';
                case 'jpg':
                case 'jpeg':
                  return 'image/jpeg';
                case 'gif':
                  return 'image/gif';
                case 'txt':
                  return 'text/plain';
                case 'mp4':
                  return 'video/mp4';
                default:
                  return 'application/octet-stream';
              }
            }

            setState(() => _isLoading[attachmentId] = true);

            try {
              // Get the repository from Riverpod using ref (not context)
              final repo = ref.read(leaveTypesRepositoryProvider);
              // Use the file name or fallback
              final fileName = attachment.fileName ?? 'attachment.pdf';
              final contentType = _getMimeTypeFromFileName(fileName);
              await repo.downloadAndOpenAbsenceAttachment(
                absencesUniqID:
                    widget.absence?.personAbsenceEntryId.toString() ?? '',
                absenceAttachmentsUniqID: attachmentId,
                fileName: fileName,
                contentType: contentType,
              );
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to download: \\${e.toString()}'),
                ),
              );
            } finally {
              if (mounted) setState(() => _isLoading[attachmentId] = false);
            }
          },
          child: Container(
            width: isFirstContainer ? screenWidth * 0.40 : screenWidth * 0.29,
            height: screenHeight * 0.045,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.lightGreyColor2, width: 1.0),
              color: Colors.transparent,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  width:
                      isFirstContainer
                          ? screenWidth * 0.03
                          : screenWidth * 0.02,
                ),
                Image.asset(
                  'assets/images/document.png',
                  width: screenWidth * 0.05,
                  height: screenHeight * 0.05,
                ),
                SizedBox(width: screenWidth * 0.01),
                Container(
                  width:
                      isFirstContainer
                          ? screenWidth * 0.20
                          : screenWidth * 0.10,
                  height: screenHeight * 0.02,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(3),
                    color: AppColors.lighWight,
                  ),
                  child: Center(
                    child: Text(
                      (attachment.fileName ?? 'Unknown.pdf'),
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: screenWidth * 0.02,
                        fontWeight: FontWeight.w400,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                SizedBox(width: screenWidth * 0.04),
                _isLoading[attachmentId] == true
                    ? CustomLoadingWidget(
                      height: 17,
                      width: 17,
                      strokeWidth: 2.0,
                    )
                    : Image.asset(
                      isFirstContainer
                          ? 'assets/images/icon_download_2.png'
                          : 'assets/images/download_icon.png',
                      height: 17,
                      width: 17,
                    ),
              ],
            ),
          ),
        ),
      );
    }

    if (widget.iconClicked == 'Apply Leave') {
      widget.date1 = formatDateWithoutOrdinal(widget.absence!.startDate);
      widget.date2 = formatDateWithoutOrdinal(widget.absence!.endDate);

      widget.remarks = widget.absence!.comment;
      AbsenceAttachments = widget.absence!.absenceAttachments ?? [];
    }

    if (widget.status == 'rejected') {
    } else if (widget.status == 'withdrawn') {
    } else if (widget.status == 'pending') {
      if (widget.SelectedTabIndex == 1 && widget.iconClicked == 'Apply Leave') {
      } else if (widget.SelectedTabIndex == 1 &&
          widget.iconClicked == 'Claim') {
      } else {}
    } else {
      if (widget.status == 'approved' &&
          widget.iconClicked == 'Claim' &&
          widget.SelectedTabIndex == 1) {
      } else {}
    }

    // Access the bottom sheet visibility state
    final isVisible = ref.watch(bottomSheetVisibilityProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Stack(
      children: [
        // Background overlay (only visible when the sheet is open)
        if (isVisible)
          GestureDetector(
            onTap: () {
              // Close the bottom sheet when background is tapped
              ref.read(bottomSheetVisibilityProvider.notifier).state = false;
              Navigator.pop(context);
            },
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 300),
              opacity: isVisible ? 0.4 : 0.0, // Fade-in/out background
              child: Container(
                color: Colors.black.withOpacity(0.5), // Dim background
              ),
            ),
          ),

        // Bottom Sheet with animated slide-in and slide-out
        AnimatedPositioned(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut, // Smooth transition
          bottom: isVisible ? 0 : -700, // Slide sheet out of view when hidden
          left: 0,
          right: 0,
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            child: SizedBox(
              child: SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight:
                        MediaQuery.of(context).size.height -
                        144, // Max height constraint
                  ),
                  child: Container(
                    width:
                        MediaQuery.of(context).size.width, // Full screen width
                    // height: containerhieght, // Fixed height
                    decoration: BoxDecoration(
                      color: AppColors.whiteColor, // Background color
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.shadowColor2,
                          blurRadius: 19, // Blur radius for the shadow
                          spreadRadius: 0, // No spread for the shadow
                          offset: const Offset(0, 0), // Shadow offset
                        ),
                      ],
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.only(
                          left: 20,
                          right: 20,
                          bottom: 20,
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(height: 12),
                              CustomSvgImage(imageName: 'ic_draghandler'),
                              isLoadingAsync.when(
                                data:
                                    (isLoading) =>
                                        isLoading
                                            ? Center(
                                              child: CustomLoadingWidget(),
                                            )
                                            : Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                        top: 15,
                                                      ),
                                                  child: Row(
                                                    children: [
                                                      Column(children: []),
                                                      SizedBox(
                                                        width: 10,
                                                      ), // Space between the text and the icon
                                                    ],
                                                  ),
                                                ),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    DmSansText(
                                                      widget.iconClicked ==
                                                              'Claim'
                                                          ? 'Time of leave'
                                                          : widget.iconClicked ==
                                                              'Letter request'
                                                          ? "${widget.LeaveData!.name}"
                                                          : capitalizeFirstWordOnly(
                                                            "${widget.absence!.absenceType}",
                                                          ),
                                                      fontSize: 20,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                    GestureDetector(
                                                      onTap: () {
                                                        showModalBottomSheet(
                                                          context: context,
                                                          isScrollControlled:
                                                              true,
                                                          builder: (context) {
                                                            return FormLeave(
                                                              isEdit: true,
                                                              isDraft: false,
                                                              leaveTypes:
                                                                  widget
                                                                      .leaveTypes,
                                                              absence:
                                                                  widget
                                                                      .absence,
                                                              leaveType:
                                                                  widget
                                                                      .absence
                                                                      ?.absenceType ??
                                                                  '',
                                                            );
                                                          },
                                                        );
                                                      },
                                                      child: CustomSvgImage(
                                                        imageName:
                                                            "applyleave_edit",
                                                        height: 24,
                                                        width: 24,
                                                      ),

                                                      // GestureDetector(
                                                      //   onTap: () {
                                                      //     if (widget
                                                      //             .assigntomePending ==
                                                      //         false) {
                                                      //       showModalBottomSheet(
                                                      //         context: context,
                                                      //         isScrollControlled:
                                                      //             true,
                                                      //         backgroundColor:
                                                      //             Colors
                                                      //                 .transparent,
                                                      //         barrierColor:
                                                      //             Colors
                                                      //                 .transparent,
                                                      //         builder: (context) {
                                                      //           final double
                                                      //           _heightRatio =
                                                      //               0.8;

                                                      //           return Container(
                                                      //             height:
                                                      //                 MediaQuery.of(
                                                      //                   context,
                                                      //                 ).size.height *
                                                      //                 0.93,
                                                      //             child: DraggableScrollableSheet(
                                                      //               initialChildSize:
                                                      //                   1.0,
                                                      //               minChildSize:
                                                      //                   0.97,
                                                      //               maxChildSize:
                                                      //                   1.0,
                                                      //               builder:
                                                      //                   (
                                                      //                     context,
                                                      //                     scrollController,
                                                      //                   ) => ClipRRect(
                                                      //                     borderRadius: const BorderRadius.only(
                                                      //                       topLeft: Radius.circular(
                                                      //                         20.0,
                                                      //                       ),
                                                      //                       topRight: Radius.circular(
                                                      //                         20.0,
                                                      //                       ),
                                                      //                     ),
                                                      //                     child: FormLeave(
                                                      //                       isEdit:
                                                      //                           true,
                                                      //                       isDraft:
                                                      //                           widget.status ==
                                                      //                           'Draft',
                                                      //                       leaveTypes:
                                                      //                           widget.leaveTypes,
                                                      //                       absence:
                                                      //                           widget.absence,
                                                      //                       leaveType:
                                                      //                           widget.absence?.absenceType ??
                                                      //                           '',
                                                      //                     ),
                                                      //                   ),
                                                      //             ),
                                                      //           );
                                                      //         },
                                                      //       );
                                                      //     }
                                                      //   },
                                                      //   child: CustomSvgImage(
                                                      //     imageName:
                                                      //         "applyleave_edit",
                                                      //   ),
                                                    ),
                                                  ],
                                                ),
                                                if (widget.assigntomePending ==
                                                        true &&
                                                    widget.iconClicked !=
                                                        'Letter request')
                                                  Column(
                                                    children: [
                                                      InfoRow(
                                                        label: 'Leave balance',
                                                        value: '12',
                                                        condition:
                                                            widget.assigntomePending ==
                                                                true &&
                                                            widget.iconClicked !=
                                                                'Letter request', // Conditional rendering
                                                        isBold: true,
                                                      ),
                                                      InfoRow(
                                                        label: 'Submitted by',
                                                        value:
                                                            'Amira Al Mansoori',
                                                        isBold: true,
                                                      ),
                                                      InfoRow(
                                                        label: 'Applied for',
                                                        value:
                                                            '$widget.date1 - $widget.date2',
                                                      ),
                                                      InfoRow(
                                                        label: 'Duration',
                                                        value:
                                                            '${widget.absence?.duration ?? 0} ${widget.absence?.duration == 1 ? 'day' : 'days'}',
                                                      ),
                                                    ],
                                                  ),

                                                if (widget.iconClicked ==
                                                        'Claim' &&
                                                    widget.SelectedTabIndex !=
                                                        1)
                                                  Column(
                                                    children: [
                                                      InfoRow(
                                                        label: 'Day worked on',
                                                        value: '1st July, 2024',
                                                        isBold: true,
                                                      ),
                                                    ],
                                                  ),

                                                if (widget.iconClicked ==
                                                        'Claim' &&
                                                    widget.SelectedTabIndex ==
                                                        1)
                                                  Column(
                                                    children: [
                                                      InfoRow(
                                                        label: 'Submitted by',
                                                        value:
                                                            'Amira Al Mansoori',
                                                        isBold: true,
                                                      ),
                                                      InfoRow(
                                                        label:
                                                            widget.status ==
                                                                    'pending'
                                                                ? 'Duration'
                                                                : 'Day worked on',
                                                        value:
                                                            widget.status ==
                                                                    'pending'
                                                                ? '2 days'
                                                                : '24 July 2024',
                                                      ),
                                                    ],
                                                  ),

                                                if (widget.iconClicked ==
                                                    'Letter request')
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                          top: 12,
                                                        ),
                                                    child: Column(
                                                      children: [
                                                        if (widget
                                                                .SelectedTabIndex ==
                                                            1)
                                                          InfoRow(
                                                            label:
                                                                widget.status ==
                                                                        'pending'
                                                                    ? 'Submitted by'
                                                                    : 'Requested by',
                                                            value:
                                                                widget
                                                                    .LeaveData
                                                                    ?.SubmittedBy ??
                                                                '',
                                                            isBold: true,
                                                            condition: true,
                                                          ),
                                                        InfoRow(
                                                          label: 'Addressed To',
                                                          value:
                                                              widget
                                                                  .LeaveData
                                                                  ?.AddressedTo ??
                                                              '',
                                                          isBold: true,
                                                          condition: true,
                                                        ),
                                                      ],
                                                    ),
                                                  )
                                                else if (widget
                                                            .assigntomePending !=
                                                        true &&
                                                    widget.iconClicked !=
                                                        'Claim')
                                                  Column(
                                                    children: [
                                                      InfoRow(
                                                        label: 'Applied for',
                                                        value:
                                                            '${widget.date1} - ${widget.date2}',
                                                        isBold: true,
                                                      ),
                                                      InfoRow(
                                                        label: 'Duration',
                                                        value:
                                                            '${widget.absence?.duration ?? 0} ${widget.absence?.duration == 1 ? 'day' : 'days'}',
                                                        isBold: true,
                                                      ),
                                                    ],
                                                  ),

                                                SizedBox(height: 16),
                                                Divider(
                                                  color:
                                                      AppColors
                                                          .tcktdetailsdividerColor,
                                                ),
                                                SizedBox(height: 12),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  children: [
                                                    Expanded(
                                                      child: Text(
                                                        widget.SelectedTabIndex ==
                                                                1
                                                            ? '"Kindly grant me casual leave for 21/04/2024 to 22/04/2024 , thank you!"'
                                                            : widget.iconClicked ==
                                                                'Letter request'
                                                            ? "${widget.LeaveData!.Description}"
                                                            : widget.remarks !=
                                                                    null &&
                                                                widget
                                                                    .remarks!
                                                                    .isNotEmpty
                                                            ? widget
                                                                .remarks! // Show remarks if it's not null or empty
                                                            : 'no remarks', // Otherwise, show a space
                                                        style: GoogleFonts.openSans(
                                                          fontSize: 12.0,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          color:
                                                              widget.remarks ==
                                                                      null
                                                                  ? AppColors
                                                                      .lightGreyColor
                                                                  : AppColors
                                                                      .blackColor,
                                                        ),
                                                        softWrap: true,
                                                        overflow:
                                                            TextOverflow.clip,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 8),
                                                // Ensure attachmentIDS is fully loaded before using it
                                                attachmentIDS.when(
                                                  data: (ids) {
                                                    if (attachmentIDS
                                                            .value!
                                                            .length !=
                                                        AbsenceAttachments
                                                            .length) {
                                                      return Center(
                                                        child: OpenSansText(
                                                          'Update in progress, please try again later.',
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      );
                                                    }
                                                    return Column(
                                                      children: [
                                                        for (
                                                          int i = 0;
                                                          i <
                                                              AbsenceAttachments
                                                                  .length;
                                                          i += 2
                                                        )
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .start,
                                                            children: [
                                                              // First Container (Always exists)
                                                              buildAttachmentContainer(
                                                                AbsenceAttachments[i],
                                                                screenWidth,
                                                                screenHeight,
                                                                true,
                                                                ids[i],
                                                                widget
                                                                    .absence!
                                                                    .personAbsenceEntryId,
                                                              ), // Now 'ids' is a normal List<String>, and we can access it using [i]
                                                              // Second Container (Only if there is a next item)
                                                              if (i + 1 <
                                                                  AbsenceAttachments
                                                                      .length)
                                                                SizedBox(
                                                                  width:
                                                                      screenWidth *
                                                                      0.03,
                                                                ),
                                                              if (i + 1 <
                                                                  AbsenceAttachments
                                                                      .length)
                                                                buildAttachmentContainer(
                                                                  AbsenceAttachments[i +
                                                                      1],
                                                                  screenWidth,
                                                                  screenHeight,
                                                                  false,
                                                                  ids[i + 1],
                                                                  widget
                                                                      .absence!
                                                                      .personAbsenceEntryId,
                                                                ),
                                                            ],
                                                          ),
                                                      ],
                                                    );
                                                  },
                                                  loading:
                                                      () => Center(
                                                        child: SizedBox(),
                                                      ), // Show loader while waiting
                                                  error:
                                                      (
                                                        error,
                                                        stackTrace,
                                                      ) => Text(
                                                        "Error loading attachments",
                                                      ), // Handle errors
                                                ),

                                                SizedBox(height: 12),
                                                Divider(
                                                  color:
                                                      AppColors
                                                          .tcktdetailsdividerColor,
                                                ),
                                                SizedBox(height: 8),

                                                taskDetailsAsync.when(
                                                  data: (taskDetails) {
                                                    if (taskNumber.isEmpty ||
                                                        filteredTask == null) {
                                                      return Center(
                                                        child: OpenSansText(
                                                          'Update in progress, please try again later.',
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      );
                                                    }

                                                    return taskHistoryAsync.when(
                                                      data: (history) {
                                                        final steps =
                                                            combineTaskDetailsAndHistory(
                                                              filteredTask!,
                                                              history.items,
                                                            );
                                                        // Check for 'withdrawn' status and non-empty reason in the last item
                                                        String? rejectedReason;
                                                        if (history
                                                            .items
                                                            .isNotEmpty) {
                                                          final lastItem =
                                                              history
                                                                  .items
                                                                  .last;
                                                          final lastAction =
                                                              lastItem.action
                                                                  ?.toLowerCase();
                                                          final lastReason =
                                                              lastItem.reason;
                                                          if (lastAction ==
                                                                  'task completed - rejected' &&
                                                              lastReason !=
                                                                  null &&
                                                              lastReason
                                                                  .isNotEmpty) {
                                                            rejectedReason =
                                                                lastReason;
                                                          }
                                                        }

                                                        var createdBy =
                                                            taskDetails
                                                                ?.createdBy ??
                                                            'Unknown';
                                                        return CustomStepper(
                                                          isCreatedByMeOnly:
                                                              _permissionChecker.GetPersonId() ==
                                                              createdBy,
                                                          iconClicked:
                                                              widget
                                                                  .iconClicked,
                                                          steps: steps,
                                                        );
                                                      },
                                                      loading:
                                                          () => const Center(
                                                            child:
                                                                CustomLoadingWidget(),
                                                          ),
                                                      error:
                                                          (err, stack) => Text(
                                                            'Task History Error: $err',
                                                          ),
                                                    );
                                                  },
                                                  loading:
                                                      () => const Center(
                                                        child:
                                                            CustomLoadingWidget(),
                                                      ),
                                                  error:
                                                      (err, stack) => Text(
                                                        'Task Details Error: $err',
                                                      ),
                                                ),

                                                // if (widget.status == 'rejected' &&
                                                //     widget.SelectedTabIndex != 1 &&
                                                //     taskNumber.isNotEmpty)
                                                //   Row(
                                                //     mainAxisAlignment:
                                                //         MainAxisAlignment.end,
                                                //     children: [
                                                //       OpenSansText(
                                                //           'Leave declined due to staffing constraints.',
                                                //           fontSize: 12.0,
                                                //           fontWeight: FontWeight.w400,
                                                //           color: Color(0XFFFF6332)),
                                                //     ],
                                                //   ),
                                                if (widget.status ==
                                                        'pending' &&
                                                    widget.SelectedTabIndex ==
                                                        1 &&
                                                    (widget.iconClicked ==
                                                            'Apply Leave' ||
                                                        widget.iconClicked ==
                                                            'Claim'))
                                                  Column(
                                                    children: [
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .start,
                                                        children: [
                                                          HeadingText(
                                                            text: 'Remarks',
                                                          ),
                                                        ],
                                                      ),
                                                      const SizedBox(
                                                        height: 8,
                                                      ), // Increased spacing

                                                      if ((widget.assigntomePending ==
                                                                  true &&
                                                              widget.status !=
                                                                  'approved') ||
                                                          (widget.status ==
                                                                  'withdrawn' &&
                                                              widget.SelectedTabIndex ==
                                                                  1) ||
                                                          (widget.iconClicked ==
                                                                  'Claim' &&
                                                              widget.status ==
                                                                  'pending' &&
                                                              widget.SelectedTabIndex ==
                                                                  1) ||
                                                          (widget.iconClicked !=
                                                                  'Letter request' &&
                                                              widget.status !=
                                                                  'pending') ||
                                                          (widget.iconClicked ==
                                                                  'Letter request' &&
                                                              widget.SelectedTabIndex ==
                                                                  1))
                                                        Column(
                                                          children: [
                                                            SizedBox(
                                                              height: 24,
                                                            ),
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .start,
                                                              children: [
                                                                // buildHeading(
                                                                //   'Attachment',
                                                                // ),
                                                                HeadingText(
                                                                  text:
                                                                      'Attachments',
                                                                ),
                                                              ],
                                                            ),
                                                            const SizedBox(
                                                              height: 8,
                                                            ),
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .start,
                                                              children: [
                                                                AttachmentField(
                                                                  onFilesChanged:
                                                                      (p0) {},
                                                                  // onFilesUpdated:
                                                                  // _updateUploadedFiles,
                                                                  uploadedFiles:
                                                                      [],
                                                                  // Pass the callback
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                        ),
                                                    ],
                                                  ),

                                                if ((widget.status == 'pending'
                                                    //  ||
                                                    //       status == 'withdrawn'
                                                    ) ||
                                                    (widget.status ==
                                                            'approved' &&
                                                        widget.SelectedTabIndex !=
                                                            1))
                                                  // if (SelectedTabIndex == "approved" ||
                                                  //     assigntomePending == false)
                                                  if ((widget.assigntomePending ==
                                                          false &&
                                                      widget.status !=
                                                          'approved'))
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                            top: 24,
                                                          ),
                                                      child: Column(
                                                        children: [
                                                          Consumer(
                                                            builder: (
                                                              context,
                                                              ref,
                                                              child,
                                                            ) {
                                                              final isSubmitting =
                                                                  ref.watch(
                                                                    isSubmittingProvider,
                                                                  );

                                                              return ElevatedButton(
                                                                onPressed: () async {
                                                                  widget.SelectedTabIndex !=
                                                                          1
                                                                      ? await WithdrawRequest(
                                                                        ref,
                                                                      )
                                                                      : () {};
                                                                  Navigator.push(
                                                                    context,
                                                                    MaterialPageRoute(
                                                                      builder:
                                                                          (
                                                                            context,
                                                                          ) =>
                                                                              ApplyLeave(),
                                                                    ),
                                                                    // This removes all previous routes
                                                                  );
                                                                },
                                                                style: ElevatedButton.styleFrom(
                                                                  minimumSize:
                                                                      const Size(
                                                                        double
                                                                            .infinity,
                                                                        53,
                                                                      ), // Increase the height
                                                                  shape: RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                          8,
                                                                        ), // Rounded corners
                                                                  ),
                                                                  backgroundColor: Color(
                                                                    0xFF395062,
                                                                  ), // Dynamic background color
                                                                  foregroundColor:
                                                                      AppColors
                                                                          .whiteColor, // Text color
                                                                ),
                                                                child:
                                                                    isSubmitting
                                                                        ? const SizedBox(
                                                                          width:
                                                                              24,
                                                                          height:
                                                                              24,
                                                                          child: CircularProgressIndicator(
                                                                            color:
                                                                                AppColors.whiteColor,
                                                                            strokeWidth:
                                                                                2,
                                                                          ),
                                                                        )
                                                                        : DmSansText(
                                                                          widget.SelectedTabIndex ==
                                                                                  1
                                                                              ? 'Approve'
                                                                              : 'Withdraw',
                                                                          fontWeight:
                                                                              FontWeight.w600,
                                                                          fontSize:
                                                                              16,
                                                                          color:
                                                                              AppColors.whiteColor,
                                                                        ),
                                                              );
                                                            },
                                                          ),
                                                          SizedBox(height: 8),
                                                          if (widget.SelectedTabIndex ==
                                                                  1 ||
                                                              widget.assigntomePending ==
                                                                      false &&
                                                                  (widget.assigntomePending ==
                                                                          true &&
                                                                      widget.status !=
                                                                          'approved'))
                                                            OutlinedButton(
                                                              onPressed: () async {
                                                                // SelectedTabIndex != 1
                                                                // ?

                                                                // : () {};

                                                                Navigator.pop(
                                                                  context,
                                                                );
                                                              },
                                                              style: OutlinedButton.styleFrom(
                                                                minimumSize: Size(
                                                                  double
                                                                      .infinity,
                                                                  53,
                                                                ), // Increase the height
                                                                shape: RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                        8,
                                                                      ), // Rounded corners
                                                                ),
                                                                side: BorderSide(
                                                                  color:
                                                                      AppColors
                                                                          .viewColor,
                                                                ), // Outline color
                                                                foregroundColor:
                                                                    AppColors
                                                                        .viewColor, // Text color
                                                              ),
                                                              child: DmSansText(
                                                                'Reject',
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                              ),
                                                            ),
                                                        ],
                                                      ),
                                                    ),
                                              ],
                                            ),
                                loading:
                                    () => Center(child: CustomLoadingWidget()),
                                error: (err, stack) => Text('Error: $err'),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
