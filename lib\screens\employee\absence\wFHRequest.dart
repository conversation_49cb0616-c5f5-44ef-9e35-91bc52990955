import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/utils/path/dynamicLinkFieldsProvider/dynamicLinkFields.dart';
import 'package:seawork/screens/employee/absence/models/absenceRequest.dart';
import 'package:seawork/screens/employee/absence/models/planBalances.dart';
import 'package:seawork/screens/employee/absence/providers/absencesProviders.dart';
import 'package:seawork/screens/employee/absence/providers/leaveRequestProviders.dart';
import 'package:seawork/screens/dashboard/mainDashboard/mainDashboard.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/absence/saveDraftPopup.dart';
import 'package:seawork/screens/employee/absence/components/customAbsenceBalncer.dart';
import 'package:seawork/components/widget/customAttachment.dart';

import 'package:seawork/components/widget/customDatePickerField.dart';
import 'package:seawork/components/widget/customSubmitConfirmationDialog.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/components/widget/customAppbar.dart';

import 'package:seawork/screens/employee/absence/models/userInfo.dart';

class WFHRequest extends ConsumerStatefulWidget {
  String? request;
  WFHRequest({super.key, required this.request});

  @override
  ConsumerState<WFHRequest> createState() => _WFHRequestState();
}

class _WFHRequestState extends ConsumerState<WFHRequest> {
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  final TextEditingController _commentsController = TextEditingController();

  List<FileModel> uploadedFiles = [];
  int? durationInDays = 0;
  DateTime? startDate;
  DateTime? endDate;
  bool isFormValid = false;
  bool _hasFormChanges = false;
  bool _hasDateError = false;
  void _updateUploadedFiles(List<FileModel> files) {
    setState(() {
      uploadedFiles = files;
      _hasFormChanges = true;
    });
    _validateForm();
  }

  String selectedStartOption = "Full day";
  String selectedEndOption = "Full day";
  int balanceDays = 12;
  double _calculateProjectedBalance() {
    double deduction = 0;
    if (selectedStartOption == "Full day") {
      deduction += 1;
    } else {
      deduction += 0.5;
    }
    if (selectedEndOption == "Full day") {
      deduction += 1;
    } else {
      deduction += 0.5;
    }
    return balanceDays - deduction;
  }

  List<FileModel> selectedFiles = [];

  bool _areDatesFilled() {
    return _startDateController.text.isNotEmpty &&
        _endDateController.text.isNotEmpty;
  }

  void _validateForm() {
    setState(() {
      isSubmitEnabled = _areDatesFilled();
    });
  }

  bool isSubmitEnabled = false; // Initial state

  void _updateFiles(List<FileModel> files) {
    setState(() {
      uploadedFiles = files;
    });
  }

  void submitLeaveRequest(WidgetRef ref) async {
    ref.read(isSubmittingProvider.notifier).state = true; // Start Loading

    String currentTime = DateFormat('HH:mm').format(DateTime.now());

    // 🔹 Loop through each uploaded file and create attachments
    List<Map<String, String>> attachments =
        uploadedFiles.map((file) {
          return {
            "DatatypeCode": "FILE",
            "FileContents": base64Encode(file.bytes),
            "Description": "Medical Certificate",
            "CategoryName":
                "Medical", // Correcting to match your example payload
            "FileName": file.name, // Corrected file name
          };
        }).toList();

    // 🔹 List of leave types that **require** attachments
    List<String> leaveTypesRequiringAttachments = [
      'Bereavement (1st Degree)',
      'Bereavement (2nd Degree)',
      'Business mission',
      'COVID leave',
      'Sick leave',
    ];

    // ✅ Check if attachments are required but missing
    if (leaveTypesRequiringAttachments.contains(widget.request) &&
        attachments.isEmpty) {
      ref.read(isSubmittingProvider.notifier).state = false; // Stop Loading
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Attachments are required for this leave type."),
        ),
      );
      return;
    }

    // 🔹 Create the request payload
    Map<String, dynamic> annualLeaveData = {
      // "personNumber": masriPersonNumber,
      "employer": employer,
      "absenceType": widget.request,
      "startDate": startDate.toString(),
      "startTime": currentTime,
      "endDate": endDate.toString(),
      "endTime": currentTime,
      "startDateDuration": 1,
      "endDateDuration": 1,
      "absenceStatusCd": "SUBMITTED",
      "comments": _commentsController.text,
      "absenceAttachments": attachments,
      // Ensure attachments are converted to JSON
    };

    print('Leave Type: ${widget.request}');
    print('-----------Attachments------------${attachments}');
    print('-----------annualLeaveData------------${annualLeaveData}');

    AbsenceRequest annualLeaveRequest = AbsenceRequest.fromAbsenceType(
      widget.request!,
      annualLeaveData,
    );

    try {
      // 🔹 Step 1: Post Leave Request & Get Unique ID
      dynamic absenceRecord = await ref.read(
        leaveRequestProvider(annualLeaveRequest).future,
      );
      String absencesUniqID = absenceRecord['personAbsenceEntryId'].toString();
      print('absenceuniqid========$absencesUniqID');

      // 🔹 Step 2: Show Success Message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Leave Request Submitted Successfully")),
      );

      // 🔹 Step 3: Show Confirmation Dialog & Navigate to Dashboard
      await showDialog(
        context: context,
        builder: (context) {
          return CustomSubmitConfirmationDialogBox(
            date1: startDate!,
            type: widget.request,
            date2: endDate!,
            onClose: () {
              context.go('/dashboard');
            },
          );
        },
      );
    } catch (error) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("Error: $error")));
    } finally {
      ref.read(isSubmittingProvider.notifier).state = false; // Stop Loading
    }
  }

  void _updateEndDate(DateTime date) {
    setState(() {
      endDate = date;
      _hasDateError = startDate != null && date.isBefore(startDate!);
    });
    _validateForm();
  }

  @override
  void initState() {
    super.initState();
    _startDateController.addListener(checkFormValidity);
    _endDateController.addListener(checkFormValidity);
    _startDateController.addListener(_onFormChanged);
    _endDateController.addListener(_onFormChanged);
    _commentsController.addListener(_onFormChanged);
  }

  @override
  void dispose() {
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  void _onFormChanged() {
    setState(() {
      _hasFormChanges = _checkForChanges();
    });
  }

  bool _checkForChanges() {
    return widget.request != null ||
        _startDateController.text.isNotEmpty ||
        _endDateController.text.isNotEmpty ||
        _commentsController.text.isNotEmpty ||
        uploadedFiles.isNotEmpty;
  }

  void checkFormValidity() {
    setState(() {
      isFormValid =
          _startDateController.text.isNotEmpty &&
          _endDateController.text.isNotEmpty;
      if (startDate != null && endDate != null) {
        _hasDateError = endDate!.isBefore(startDate!);
        isFormValid = isFormValid && !_hasDateError;
      }

      // Parse and reformat date only if text is not empty
      if (_startDateController.text.isNotEmpty) {
        DateTime startDate = DateFormat(
          'yyyy-MM-dd',
        ).parse(_startDateController.text);
        _startDateController.text = DateFormat('yyyy/MM/dd').format(startDate);
      }

      if (_endDateController.text.isNotEmpty) {
        DateTime endDate = DateFormat(
          'yyyy-MM-dd',
        ).parse(_endDateController.text);
        _endDateController.text = DateFormat('yyyy/MM/dd').format(endDate);
      }
    });
  }

  Future<void> _showSaveDraftConfirmationDialog() async {
    final result = await showDialog(
      context: context,
      builder:
          (context) => SaveDraftConfirmationDialog(
            onSaveDraft: () {
              print('Saving draft...');
              Navigator.pop(context, true);
            },
            onDelete: () {
              print('Deleting draft...');
              Navigator.pop(context, false);
            },
          ),
    );

    if (result != null) {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final planBalancesAsync = ref.watch(getPlanBalanceProvider);
    print(planBalancesAsync);

    // Extract balance based on selected leave type
    double? selectedBalance;
    double? projectedBalance;
    double? AbsenceBalance;
    List<PlanBalanceModel> previousPlanItems = [];
    void handlePlanBalances(
      AsyncValue<List<PlanBalanceModel>> planBalancesAsync,
      String leaveType,
      Function(double?) onBalanceUpdated,
    ) {
      planBalancesAsync.when(
        data: (planBalances) {
          // Ensure that planBalances is a List<PlanBalanceModel>
          if (planBalances.isEmpty) {
            onBalanceUpdated(-1);
            return;
          }

          // Check if any plan matches the leaveType
          final hasMatchingPlan = planBalances.any(
            (plan) => plan.planName == leaveType,
          );

          if (!hasMatchingPlan) {
            // If no matching plan found, set balance to -1
            onBalanceUpdated(-1);
            print("Absence Balance: -1 (No matching leave type)");
            return;
          }

          // Find the matching plan
          final matchingPlan = planBalances.firstWhere(
            (plan) => plan.planName == leaveType,
          );

          // Compute AbsenceBalance
          final selectedBalance =
              matchingPlan.balanceAsOfBalanceCalculationDate;
          final absenceBalance =
              selectedBalance != null ? selectedBalance.ceilToDouble() : -1;

          print("Absence Balance: $absenceBalance");

          // Pass the balance back via callback function
          onBalanceUpdated(absenceBalance as double?);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) {
          print("Error: $err");
          onBalanceUpdated(-1);
        },
      );
    }

    handlePlanBalances(ref.watch(getPlanBalanceProvider), widget.request!, (
      balance,
    ) {
      setState(() {
        AbsenceBalance = balance;
      });
    });
    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(title: 'Work from home'),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                CustomTextFieldWithHeading(
                  Heading: 'Absence type',
                  hintText: 'Work from home',
                  initialValue: 'Work from home',
                  hasAsterisk: true,
                  fontSize: 14,
                  fontFamily: 'Open Sans',
                  fillColor: AppColors.whiteColor,
                  hintColor: AppColors.lightGreyshade,
                  hintStyle: AppColors.blackColor,
                  readOnly: true, // Make it non-editable
                  enabled: false, // Optional: make it appear disabled
                ),
                SizedBox(height: 8),
                AbsenceBalanceWidget(absenceType: '', balance: AbsenceBalance),
                CustomDatePickerField(
                  dateText: "Start date",
                  hintText: "Select Start Date",
                  controller: _startDateController,
                  onDateSelected: (selectedDate) {
                    setState(() {
                      startDate = selectedDate;
                    });
                  },
                  isStartDate: true,
                ),
                SizedBox(height: 24),
                CustomDatePickerField(
                  dateText: "End date",
                  hintText: "Select End Date",
                  controller: _endDateController,
                  onDateSelected: _updateEndDate,
                  isStartDate: false,
                  startDate: startDate, // Pass the selected start date
                ),
                if (_hasDateError)
                  Padding(
                    padding: EdgeInsets.only(top: 4),
                    child: Align(
                      alignment: Alignment.topLeft,
                      child: OpenSans400Large(
                        12,
                        "End date must be greater than or equal to start date",

                        AppColors.redColor,
                      ),
                    ),
                  ),

                const SizedBox(height: 6),
                if (!_hasDateError && startDate != null && endDate != null)
                  DurationBalanceWidget(
                    onProjectedBalanceUpdated: (p0) {
                      projectedBalance = p0;
                    },
                    onDurationCalculated: (p0) {
                      durationInDays = p0;
                    },
                    absenceBalance: AbsenceBalance,
                    startDate: startDate!,
                    endDate: endDate,
                  ),
                CustomTextFieldWithHeading(
                  controller: _commentsController,
                  Heading: 'Comments',
                  hintText: 'Enter comments',
                  hasAsterisk: false,
                  maxlines: 3,
                  fontSize: 14,
                  maxLength: 500,
                  fontFamily: 'Open Sans',
                  fillColor: AppColors.whiteColor,
                  hintColor: AppColors.lightGreyshade,
                  hintStyle: AppColors.lightGreyshade,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                ),
                SizedBox(height: 24),
                HeadingText(text: 'Attachments'),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: AttachmentField(
                        uploadedFiles: uploadedFiles,
                        onFilesChanged: _updateUploadedFiles,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24),

                DynamicLinkFields(),
                SizedBox(height: 32),
                // _buildSubmitButton(isSubmitEnabled, projectedBalance)
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: _buildSubmitButton(
        isSubmitEnabled,
        projectedBalance,
      ),
    );
  }

  Widget _buildSubmitButton(bool isSubmitEnabled, projectedBalance) {
    return Consumer(
      builder: (context, ref, child) {
        final isSubmitting = ref.watch(isSubmittingProvider);

        return Padding(
          padding: const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 32.0),

          // padding: const EdgeInsets.symmetric(),
          child: ElevatedButton(
            onPressed:
                isFormValid && !isSubmitting
                    ? () {
                      submitLeaveRequest(ref);
                    }
                    : null,
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 56),
              backgroundColor:
                  isFormValid && !isSubmitting
                      ? AppColors.viewColor
                      : AppColors.lightGreyColor2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child:
                isSubmitting
                    ? CircularProgressIndicator(color: AppColors.whiteColor)
                    : Padding(
                      padding: const EdgeInsets.only(top: 21, bottom: 18),
                      child: DmSansText(
                        "Submit request",
                        // style: GoogleFonts.dmSans(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        // height: 20.83 / 16,
                        color:
                            isFormValid && !isSubmitting
                                ? AppColors.whiteColor
                                : AppColors.lightGreyshade,
                        // ),
                      ),
                    ),
          ),
        );
      },
    );
  }
}
