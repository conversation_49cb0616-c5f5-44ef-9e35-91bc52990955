import 'package:dio/dio.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/employee/absence/models/absenceRequest.dart';
import 'package:seawork/screens/employee/absence/models/planBalances.dart';

class LeaveRequestRepository {
  final Dio _dio;

  LeaveRequestRepository(this._dio);

  Future<dynamic> postLeaveRequest(AbsenceRequest absenceRequest) async {
    final response;

    try {
      response = await _dio.post(
        '${baseUrlEMSProvider}/absences', // Replace with your actual API URL
        data: absenceRequest.toJson(), // Send JSON payload
      );

      // Handle response
      if (response.statusCode == 200 || response.statusCode == 201) {
      } else {}
      return response.data;
    } on DioException catch (e) {
      print('Error posting leave request: ${e.message}');
      print('Response data: ${e.response?.data}');
      final errorMessage =
          e.response?.data['details'] ?? 'Something went wrong';
      throw DioException(
        requestOptions: e.requestOptions,
        response: e.response,
        error: errorMessage,
      );
    }
  }

  Future<dynamic> patchLeaveRequest(
    Map<String, dynamic> absenceRequest,
    String absecenceUniqID,
  ) async {
    final response;

    try {
      response = await _dio.patch(
        '${baseUrlEMSProvider}/absences/$absecenceUniqID', // Replace with your actual API URL
        data: absenceRequest, // Send JSON payload
      );

      // Handle response
      if (response.statusCode == 200 || response.statusCode == 201) {
      } else {}
      return response.data;
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data['details'] ?? 'Something went wrong';
      throw DioException(
        requestOptions: e.requestOptions,
        response: e.response,
        error: errorMessage,
      );
    } catch (e) {
      throw Exception('Failed to update Leave request');
    }
  }

  Future<PlanBalanceModel> getPlanBalance() async {
    try {
      final response = await _dio.get('${baseUrlEMSProvider}/planbalances');
      return PlanBalanceModel.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to fetch Plan Balance');
    }
  }
}
