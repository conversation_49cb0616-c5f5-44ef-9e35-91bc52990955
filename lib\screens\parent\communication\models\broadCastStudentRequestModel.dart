import 'package:json_annotation/json_annotation.dart';
part 'broadCastStudentRequestModel.g.dart';

@JsonSerializable()
class BroadCastStudentRequest {
  final int? nurseryId;
  final int? classId;
  final int? sectionId;
  BroadCastStudentRequest({this.nurseryId, this.classId, this.sectionId});

  factory BroadCastStudentRequest.fromJson(Map<String, dynamic> json) =>
      _$BroadCastStudentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$BroadCastStudentRequestToJson(this);
}
