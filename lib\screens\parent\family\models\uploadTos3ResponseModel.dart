import 'package:freezed_annotation/freezed_annotation.dart';
part 'uploadTos3ResponseModel.g.dart';

@JsonSerializable()
class UploadTos3 {
  @Json<PERSON>ey(name: "result")
  final String result;
  @<PERSON><PERSON><PERSON><PERSON>(name: "fileKeyName")
  final String fileKeyName;
  @<PERSON><PERSON><PERSON><PERSON>(name: "thumbnail<PERSON>ey")
  final String thumbnailKey;
  @<PERSON><PERSON><PERSON><PERSON>(name: "fileSize")
  final String fileSize;

  UploadTos3({
    required this.result,
    required this.fileKeyName,
    required this.thumbnailKey,
    required this.fileSize,
  });

  UploadTos3 copyWith({
    String? result,
    String? fileKeyName,
    String? thumbnailKey,
    String? fileSize,
  }) => UploadTos3(
    result: result ?? this.result,
    fileKeyName: fileKeyName ?? this.fileKeyName,
    thumbnailKey: thumbnailKey ?? this.thumbnailKey,
    fileSize: fileSize ?? this.fileSize,
  );

  factory UploadTos3.fromJson(Map<String, dynamic> json) =>
      _$UploadTos3<PERSON>rom<PERSON><PERSON>(json);

  Map<String, dynamic> toJson() => _$UploadTos3ToJson(this);
}
