import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:seawork/data/preferencesUtils.dart';

Future<void> downloadFileWithDio({
  required Dio dio,
  required String url,
  required String fileName,
  required dynamic context, // Pass BuildContext if you want to show SnackBar
}) async {
  try {
    final sessionToken = await PreferencesUtils.getString(
      PreferencesUtils.SESSION_TOKEN,
    );

    // Fetch the file as bytes
    final response = await dio.get<List<int>>(
      url,
      options: Options(
        responseType: ResponseType.bytes,
        headers: {
          'Authorization': 'Bearer $sessionToken',
          'Accept': 'application/pdf',
        },
      ),
    );

    final bytes = response.data;
    if (bytes == null) {
      throw Exception('No data received from server');
    }

    if (kIsWeb) {
      await FileSaver.instance.saveFile(
        name: fileName,
        bytes: Uint8List.fromList(bytes),
        ext: 'pdf',
        mimeType: MimeType.pdf,
      );
    } else {
      await FileSaver.instance.saveAs(
        name: fileName,
        bytes: Uint8List.fromList(bytes),
        ext: 'pdf',
        mimeType: MimeType.pdf,
      );
      if (context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('File downloaded successfully: $fileName')),
        );
      }
    }
  } on DioException catch (dioError) {
    String errorMsg = 'Download failed: ${dioError.error?.toString() ?? dioError.message}';
    if (dioError.response != null) {
      errorMsg +=
          '\nStatus code: ${dioError.response?.statusCode}\n'
          'Response: ${dioError.response?.data}';
    }
    if (context != null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(errorMsg)));
    }
    rethrow;
  } catch (e) {
    if (context != null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Download failed: $e')));
    }
  }
}
