import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/parent/education/models/nurseryModel.dart';
import 'package:seawork/screens/parent/education/models/sectionModel.dart';

@Injectable()
class EducationRepository {
  final Dio _dio;

  EducationRepository(this._dio);
  Future<List<Nursery>> getNurseriesAutocomplete() async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/student/getNurseriesAutocomplete',
        queryParameters: {},
      );
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => Nursery.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load Nursery');
    } catch (e) {
      throw Exception('Failed to load Nursery');
    }
  }

  Future<List<Section>> getAvailableSectionsWithRemainingSeat({
    int? studentId,
  }) async {
    try {
      final response = await _dio.get(
        '${baseUrlPMSProvider}/student/getAvailableSectionsWithRemainingSeat',
        queryParameters: {"studentId": studentId},
      );
      if (response.statusCode == 200) {
        if (response.data is List) {
          return (response.data as List)
              .map((item) => Section.fromJson(item))
              .toList();
        }
      }
      throw Exception('Failed to load Section');
    } catch (e) {
      throw Exception('Failed to load Section');
    }
  }
}
