import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/appTextFieldDecoration.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/widget/customCalendar.dart';

class CustomDatePickerField extends StatefulWidget {
  final String hintText;
  final bool hasAsterisk;
  final TextEditingController controller;
  final Function(DateTime) onDateSelected;
  final bool isStartDate;
  final String dateText;
  final DateTime? startDate;
  final bool enabled;
  final DateTime? endDate;
  final bool allowPastDates;
  final String? Function(String?)? validator;

  const CustomDatePickerField({
    Key? key,
    required this.dateText,
    required this.hintText,
    required this.controller,
    required this.onDateSelected,
    this.hasAsterisk = true,
    this.isStartDate = true,
    this.startDate,
    this.endDate,
    this.enabled = true,
    this.allowPastDates = true,
    this.validator,
  }) : super(key: key);

  @override
  _CustomDatePickerFieldState createState() => _CustomDatePickerFieldState();
}

class _CustomDatePickerFieldState extends State<CustomDatePickerField> {
  void _showCalendarPopup() {
    if (!widget.enabled) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(16),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: CustomCalendar(
              initialDate: _getInitialDate(),
              minDate: _getMinDate(),
              maxDate: _getMaxDate(),
              allowPastDates: widget.allowPastDates,
              selectableDayPredicate: _selectableDayPredicate,
              onSingleDateSelected: _onDateSelected,
              onApplyFilter: _onApplyFilter,
              onCancel: _onCancel,
              applyButtonText: 'Select',
              cancelButtonText: 'Cancel',
              showCancelButton: true,
            ),
          ),
        );
      },
    );
  }

  void _onDateSelected(DateTime selectedDate) {
    final formattedDate = DateFormat('dd/MM/yyyy').format(selectedDate);
    setState(() {
      widget.controller.text = formattedDate;
    });
    widget.onDateSelected(selectedDate);
  }

  void _onApplyFilter() {
    Navigator.of(context).pop(); // Close the popup
  }

  void _onCancel() {
    Navigator.of(context).pop(); // Close the popup
  }

  DateTime? _getInitialDate() {
    if (widget.controller.text.isNotEmpty) {
      try {
        return DateFormat('dd/MM/yyyy').parse(widget.controller.text);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  DateTime _getMinDate() {
    final DateTime today = DateTime.now();
    final DateTime todayNormalized = DateTime(
      today.year,
      today.month,
      today.day,
    );

    if (widget.isStartDate) {
      // For start date: can only select today or future dates (unless allowPastDates is true)
      return widget.allowPastDates ? DateTime(1900) : todayNormalized;
    } else {
      // For end date: must be equal to or after start date
      return widget.startDate ??
          (widget.allowPastDates ? DateTime(1900) : todayNormalized);
    }
  }

  DateTime _getMaxDate() {
    // For start date: max selectable is the day before end date (if set)
    if (widget.isStartDate && widget.endDate != null) {
      return widget.endDate!.subtract(const Duration(days: 1));
    }
    // For end date: no upper limit, or set as needed
    return DateTime(2100);
  }

  bool _selectableDayPredicate(DateTime date) {
    final DateTime today = DateTime.now();
    final DateTime todayNormalized = DateTime(
      today.year,
      today.month,
      today.day,
    );
    final DateTime dateNormalized = DateTime(date.year, date.month, date.day);

    if (widget.isStartDate) {
      // Start date: must be before end date (if set)
      if (widget.endDate != null) {
        final DateTime endDateNormalized = DateTime(
          widget.endDate!.year,
          widget.endDate!.month,
          widget.endDate!.day,
        );
        if (!dateNormalized.isBefore(endDateNormalized)) {
          return false;
        }
      }
      if (!widget.allowPastDates && dateNormalized.isBefore(todayNormalized)) {
        return false;
      }
      return true;
    } else {
      // End date: must be after start date (if set)
      if (widget.startDate != null) {
        final DateTime startDateNormalized = DateTime(
          widget.startDate!.year,
          widget.startDate!.month,
          widget.startDate!.day,
        );
        if (!dateNormalized.isAfter(startDateNormalized)) {
          return false;
        }
      }
      if (!widget.allowPastDates && dateNormalized.isBefore(todayNormalized)) {
        return false;
      }
      return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        HeadingText(text: widget.dateText, hasAsterisk: widget.hasAsterisk),
        const SizedBox(height: 8),
        TextFormField(
          enabled: widget.enabled,
          style: const TextStyle(
            fontFamily: 'Open Sans',
            fontSize: 14,
            fontWeight: FontWeight.w400,
            height: 19 / 14,
            color: Colors.black,
          ),
          controller: widget.controller,
          decoration: AppTextFieldDecoration.defaultDecoration.copyWith(
            hintText: widget.hintText,
            hintStyle: GoogleFonts.openSans(
              color: AppColors.lightGreyshade,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
            filled: true,
            fillColor: AppColors.whiteColor,
            contentPadding: const EdgeInsets.only(left: 20),
            suffixIcon: Padding(
              padding: const EdgeInsets.only(right: 20),
              child: CustomSvgImage(imageName: "calendar"),
            ),
          ),
          readOnly: true,
          onTap: _showCalendarPopup,
          validator:
              widget.validator ??
              (value) {
                if (value == null || value.isEmpty) {
                  return widget.isStartDate
                      ? 'Please select start date'
                      : 'Please select end date';
                }

                // Only allow parsing if the value matches the expected pattern
                final regex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
                if (!regex.hasMatch(value)) {
                  return 'Invalid date format';
                }
                try {
                  DateTime selectedDate = DateFormat(
                    'dd/MM/yyyy',
                  ).parseStrict(value);

                  if (widget.isStartDate) {
                    // If endDate is set, start date must be less than end date
                    if (widget.endDate != null) {
                      final DateTime endDateNormalized = DateTime(
                        widget.endDate!.year,
                        widget.endDate!.month,
                        widget.endDate!.day,
                      );
                      final DateTime selectedDateNormalized = DateTime(
                        selectedDate.year,
                        selectedDate.month,
                        selectedDate.day,
                      );
                      if (!selectedDateNormalized.isBefore(endDateNormalized)) {
                        return 'Start date must be before end date';
                      }
                    }
                    // Optionally, check for past dates
                    if (!widget.allowPastDates) {
                      final DateTime todayNormalized = DateTime(
                        DateTime.now().year,
                        DateTime.now().month,
                        DateTime.now().day,
                      );
                      final DateTime selectedDateNormalized = DateTime(
                        selectedDate.year,
                        selectedDate.month,
                        selectedDate.day,
                      );
                      if (selectedDateNormalized.isBefore(todayNormalized)) {
                        return 'Start date cannot be in the past';
                      }
                    }
                  } else {
                    // For end date: must be after start date
                    if (widget.startDate != null) {
                      final DateTime startDateNormalized = DateTime(
                        widget.startDate!.year,
                        widget.startDate!.month,
                        widget.startDate!.day,
                      );
                      final DateTime selectedDateNormalized = DateTime(
                        selectedDate.year,
                        selectedDate.month,
                        selectedDate.day,
                      );
                      if (!selectedDateNormalized.isAfter(
                        startDateNormalized,
                      )) {
                        return 'End date must be after start date';
                      }
                    }
                    // Optionally, check for past dates
                    if (!widget.allowPastDates) {
                      final DateTime todayNormalized = DateTime(
                        DateTime.now().year,
                        DateTime.now().month,
                        DateTime.now().day,
                      );
                      final DateTime selectedDateNormalized = DateTime(
                        selectedDate.year,
                        selectedDate.month,
                        selectedDate.day,
                      );
                      if (selectedDateNormalized.isBefore(todayNormalized)) {
                        return 'End date cannot be in the past';
                      }
                    }
                  }
                } catch (e) {
                  return 'Invalid date format';
                }

                return null;
              },
        ),
      ],
    );
  }
}

// DurationBalanceWidget remains exactly the same as in the original code
class DurationBalanceWidget extends StatelessWidget {
  final DateTime startDate;
  final DateTime? endDate;
  final double? absenceBalance;
  final Function(double) onProjectedBalanceUpdated;
  final Function(int) onDurationCalculated;

  const DurationBalanceWidget({
    Key? key,
    required this.startDate,
    this.endDate,
    required this.onProjectedBalanceUpdated,
    required this.onDurationCalculated,
    this.absenceBalance,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double projectedBalance = 0.0;
    int duration = 0;
    if (endDate != null) {
      duration = endDate!.difference(startDate).inDays + 1;
    }
    if (absenceBalance != -1) {
      projectedBalance = absenceBalance! - duration;
    }
    onProjectedBalanceUpdated(projectedBalance);
    onDurationCalculated(duration);

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text.rich(
              TextSpan(
                text: 'Duration: ',
                style: const TextStyle(
                  fontFamily: 'Open Sans',
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Color(0XFF3E98FF),
                ),
                children: [
                  TextSpan(
                    text: '$duration',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const TextSpan(text: ' days', style: TextStyle(fontSize: 12)),
                ],
              ),
            ),
            if (absenceBalance != -1)
              Text.rich(
                TextSpan(
                  text: 'Projected balance: ',
                  style: const TextStyle(
                    fontFamily: 'Open Sans',
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.bdaybannercolor3,
                  ),
                  children: [
                    TextSpan(
                      text: '$projectedBalance',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppColors.bdaybannercolor3,
                      ),
                    ),
                    const TextSpan(
                      text: ' days',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.bdaybannercolor3,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        if (projectedBalance < 0)
          Column(
            children: [
              Row(
                children: [
                  CustomPngImage(imageName: "Infoicon", height: 18, width: 19),
                  const SizedBox(width: 5),
                  OpenSansText(
                    'Leave balance exceeded. Please adjust your dates.',
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.Orange,
                  ),
                ],
              ),
            ],
          ),
      ],
    );
  }
}
