import 'package:dio/dio.dart';

class RegistrationRepository {
  final Dio _dio;
  final String baseUrl;

  RegistrationRepository(this._dio, this.baseUrl) {
    // Set the base URL on the Dio instance
    _dio.options.baseUrl = baseUrl;
  }
  // Send OTP for registration
  Future<Map<String, dynamic>> sendSignUpMobileOtpByPass(
    String phoneNumber,
  ) async {
    try {
      print(
        'Sending registration OTP to: ${_dio.options.baseUrl}/authenticate/SendParentSignUpMobileOTPByPass',
      );
      final response = await _dio.get(
        '/authenticate/SendParentSignUpMobileOTPByPass',
        queryParameters: {'phoneNumber': phoneNumber},
        options: Options(validateStatus: (status) => status! < 500),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        final errorMessage = response.data['message'] ?? 'Failed to send OTP';
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      print('DioException: ${e.message}');
      print('DioException response: ${e.response?.data}');
      print('DioException status: ${e.response?.statusCode}');
      throw Exception('Error sending OTP: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error sending OTP: $e');
    }
  }

  // Verify OTP for registration
  Future<Map<String, dynamic>> verifySignUpMobileOtpByPass({
    required String phoneNumber,
    required String requestId,
    required String otp,
  }) async {
    try {
      final response = await _dio.get(
        '/authenticate/SignupVerifyMobileOtpByPass',
        queryParameters: {
          'phoneNumber': phoneNumber,
          'requestId': requestId,
          'otp': otp,
        },
        options: Options(validateStatus: (status) => status! < 500),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        final errorMessage = response.data['message'] ?? 'Failed to verify OTP';
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      print('DioException: ${e.message}');
      print('DioException response: ${e.response?.data}');
      print('DioException status: ${e.response?.statusCode}');
      throw Exception('Error verifying OTP: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error verifying OTP: $e');
    }
  }

  // Send OTP for registration via email
  Future<Map<String, dynamic>> sendSignUpEmailOtpByPass(String email) async {
    try {
      final response = await _dio.get(
        '/authenticate/SendParentSignUpEmailOTPByPass',
        queryParameters: {'emailId': email},
        options: Options(validateStatus: (status) => status! < 500),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to send email OTP';
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      throw Exception('Error sending email OTP: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error sending email OTP: $e');
    }
  }

  // Verify OTP for registration via email
  Future<Map<String, dynamic>> verifySignUpEmailOtp({
    required String email,
    required String otp,
  }) async {
    try {
      final response = await _dio.get(
        '/authenticate/SignupVerifyEmailOtp',
        queryParameters: {'email': email, 'otp': otp},
        options: Options(validateStatus: (status) => status! < 500),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        final errorMessage =
            response.data['message'] ?? 'Failed to verify email OTP';
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      throw Exception('Error verifying email OTP: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error verifying email OTP: $e');
    }
  }

  // Sign up verify all
  Future<Map<String, dynamic>> signUpVerifyAll({
    required String mobileNo,
    required String mobileOtp,
    required dynamic requestId,
    String? email,
    String? emailOtp,
    required bool isSignupWithoutEmail,
  }) async {
    try {
      final data = {
        'mobileNo': mobileNo,
        'mobileOtp': mobileOtp,
        'requestId': requestId,
        'isSignupWithoutEmail': isSignupWithoutEmail,
        'email': '',
        'emailOtp': '',
      };
      if (!isSignupWithoutEmail) {
        data['email'] = email;
        data['emailOtp'] = emailOtp;
      }
      final response = await _dio.post(
        '/authenticate/signUpVerifyAllByPass',
        data: data,
        options: Options(validateStatus: (status) => status! < 500),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        final errorMessage = response.data['message'] ?? 'Failed to verify all';
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      throw Exception('Error in signUpVerifyAll: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error in signUpVerifyAll: $e');
    }
  }
}
