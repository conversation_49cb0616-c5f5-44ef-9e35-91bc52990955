import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
// import 'package:seawork/components/form/bottomSheet.dart';
import 'package:seawork/components/form/searchBar.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customCompactCalendar.dart';
import 'package:seawork/screens/employee/paySlip/components/payslipListitem.dart';
import 'package:seawork/screens/employee/paySlip/models/payslipModel.dart';
// import 'package:seawork/screens/employee/paySlip/paySlipBottomSheet.dart';
import 'package:seawork/screens/employee/paySlip/providers/payslipProvider.dart';
import 'package:seawork/utils/style/colors.dart';

final paySlipDateRangeProvider = StateProvider<Map<String, DateTime?>?>(
  (ref) => null,
);

class PaySlipScreen extends ConsumerStatefulWidget {
  @override
  _PaySlipScreenState createState() => _PaySlipScreenState();
}

class _PaySlipScreenState extends ConsumerState<PaySlipScreen> {
  DateTime? _startDate;
  DateTime? _endDate;
  List<PaySlip> _filteredPayslips = [];
  bool _isFiltered = false;
  bool _isSearching = false;
  String _searchQuery = '';
  bool _showCalendar = false;
  int? _downloadingPayslipId; // Track which payslip is being downloaded

  // Add these new controllers and focus node
  late TextEditingController _searchController;
  late FocusNode _searchFocusNode;
  bool _isSearchEnabled = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _searchFocusNode = FocusNode();

    // Listen to search controller changes
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
        _isSearchEnabled = _searchController.text.isNotEmpty;
      });
    });

    // Listen to focus changes
    _searchFocusNode.addListener(() {
      if (_searchFocusNode.hasFocus && _searchController.text.isEmpty) {
        setState(() {
          _isSearchEnabled = false;
        });
      }

      // Close search when focus is lost and field is empty
      if (!_searchFocusNode.hasFocus &&
          _searchController.text.isEmpty &&
          _isSearching) {
        _closeSearch();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  String _formatDate(String date) {
    try {
      final DateTime parsedDate = DateTime.parse(date);
      final DateFormat formatter = DateFormat('dd MMM, yyyy');
      return formatter.format(parsedDate);
    } catch (e) {
      return 'Invalid Date';
    }
  }

  // void _showPaySlipPreview(PaySlip paySlip) {
  //   showCustomDraggableModalBottomSheet(
  //     context: context,
  //     child: PaySlipBottomSheet(paySlip: paySlip),
  //   );
  // }

  void _applyDateFilter(List<PaySlip> allPayslips) {
    if (_startDate == null && _endDate == null) {
      setState(() {
        _isFiltered = false;
        _filteredPayslips = [];
      });
      return;
    }

    setState(() {
      _isFiltered = true;
      _filteredPayslips =
          allPayslips.where((slip) {
            final slipDate = DateTime.parse(slip.paymentDate);
            final startCondition =
                _startDate == null ||
                slipDate.isAfter(_startDate!.subtract(const Duration(days: 1)));
            final endCondition =
                _endDate == null ||
                slipDate.isBefore(_endDate!.add(const Duration(days: 1)));
            return startCondition && endCondition;
          }).toList();
    });
  }

  void _clearFilters() {
    setState(() {
      _startDate = null;
      _endDate = null;
      _isFiltered = false;
      _isSearching = false;
      _searchQuery = '';
      _filteredPayslips = [];
      _showCalendar = false;
      _isSearchEnabled = false;
    });
    _searchController.clear();
    _searchFocusNode.unfocus();
    ref.read(paySlipDateRangeProvider.notifier).state = null;
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (_isSearching) {
        // When enabling search, focus the search field
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _searchFocusNode.requestFocus();
        });
      } else {
        // When disabling search, clear everything
        _closeSearch();
      }
    });
  }

  void _closeSearch() {
    setState(() {
      _isSearching = false;
      _searchQuery = '';
      _isSearchEnabled = false;
    });
    _searchController.clear();
    _searchFocusNode.unfocus();
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = '';
      _isSearchEnabled = false;
    });
    _searchController.clear();
    // Keep focus after clearing so user can continue typing
    if (_isSearching) {
      _searchFocusNode.requestFocus();
    }
  }

  List<PaySlip> _applyFilters(List<PaySlip> allPayslips) {
    List<PaySlip> filtered = allPayslips;

    if (_isFiltered && (_startDate != null || _endDate != null)) {
      filtered = _filteredPayslips;
    }

    if (_isSearching && _searchQuery.isNotEmpty) {
      filtered =
          filtered.where((slip) {
            final formattedDate = _formatDate(slip.paymentDate).toLowerCase();
            return formattedDate.contains(_searchQuery.toLowerCase());
          }).toList();
    }

    return filtered..sort(
      (a, b) => DateTime.parse(
        b.paymentDate,
      ).compareTo(DateTime.parse(a.paymentDate)),
    );
  }

  // Handle download with loading state
  Future<void> _handleDownload(int documentsOfRecordId) async {
    setState(() {
      _downloadingPayslipId = documentsOfRecordId;
    });

    try {
      await ref
          .read(payslipsProvider.notifier)
          .downloadAndOpenPayslip(documentsOfRecordId, context);
    } catch (e) {
      // Handle error if needed
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to download payslip'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _downloadingPayslipId = null;
      });
    }
  }

  // Handle swipe to clear payslip
  void _handlePayslipDismissed(PaySlip paySlip) {
    setState(() {
      // Remove from filtered list if it exists
      if (_filteredPayslips.isNotEmpty) {
        _filteredPayslips.removeWhere(
          (slip) => slip.documentsOfRecordId == paySlip.documentsOfRecordId,
        );
      }
    });

    // You might also want to remove from the main provider data
    // ref.read(payslipsProvider.notifier).removePayslip(paySlip.documentsOfRecordId);
  }

  // Build grouped payslips list by date
  Widget _buildGroupedPayslipsList(List<PaySlip> paySlips) {
    final size = MediaQuery.of(context).size;

    // Group payslips by date
    Map<String, List<PaySlip>> groupedPayslips = {};
    for (var paySlip in paySlips) {
      final dateKey = _formatDate(paySlip.paymentDate);
      if (!groupedPayslips.containsKey(dateKey)) {
        groupedPayslips[dateKey] = [];
      }
      groupedPayslips[dateKey]!.add(paySlip);
    }

    // Sort dates in descending order
    final sortedDates =
        groupedPayslips.keys.toList()..sort((a, b) {
          final dateA = DateTime.parse(groupedPayslips[a]!.first.paymentDate);
          final dateB = DateTime.parse(groupedPayslips[b]!.first.paymentDate);
          return dateB.compareTo(dateA);
        });

    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateKey = sortedDates[index];
        final payslipsForDate = groupedPayslips[dateKey]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date header
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: size.width * 0.04,
                vertical: size.height * 0.015,
              ),
              child: OpenSansText(
                dateKey,
                fontSize: size.width * 0.04,
                fontWeight: FontWeight.w600,
                color: AppColors.textColor,
                textAlign: TextAlign.center,
              ),
            ),

            // Payslips for this date
            ...payslipsForDate
                .map(
                  (paySlip) => PaySlipListItem(
                    paySlip: paySlip,
                    formattedDate: _formatDate(paySlip.paymentDate),
                    onTap: () {},
                    //  => _showPaySlipPreview(paySlip),
                    onDownload:
                        () => _handleDownload(paySlip.documentsOfRecordId),
                    isDownloading:
                        _downloadingPayslipId == paySlip.documentsOfRecordId,
                    showDateInTitle:
                        false, // Don't show date in individual items since we have date header
                    onDismissed: () => _handlePayslipDismissed(paySlip),
                  ),
                )
                .toList(),

            SizedBox(height: size.height * 0.02),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final payslipsState = ref.watch(payslipsProvider);
    final size = MediaQuery.of(context).size;

    // Watch for date range changes from the FilterBasedOnDates component
    ref.listen(paySlipDateRangeProvider, (previous, next) {
      if (next != null) {
        setState(() {
          _startDate = next['startDate'];
          _endDate = next['endDate'];
        });

        // Apply the date filter
        ref
            .read(payslipsProvider)
            .when(
              data: (paySlips) => _applyDateFilter(paySlips),
              loading: () => null,
              error: (_, __) => null,
            );
      }
    });

    return GestureDetector(
      onTap: () {
        // Close search when tapping outside if search field is empty
        if (_isSearching && _searchController.text.isEmpty) {
          _closeSearch();
        }
        // Always unfocus to hide keyboard when tapping outside
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: CustomAppBar(title: 'Pay slip', showActionIcon: true),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: size.width * 0.05),
          child: Column(
            children: [
              SizedBox(height: size.height * 0.02),
              SearchBarWidget(
                showSearchField: _isSearching,
                searchController: _searchController,
                focusNode: _searchFocusNode, // Pass the focus node
                hintText: "Search payslips",
                onSearch: (query) {
                  // This callback might not be needed if using controller listener
                  // But keeping it for compatibility with your SearchBarWidget
                },
                onClear: _clearSearch,
                onToggleSearch: _toggleSearch,
                isSearchEnabled: _isSearchEnabled, // Pass the enabled state
                filterWidget: FilterBasedOnDates(
                  dateRangeProvider: paySlipDateRangeProvider,
                ),
              ),
              // Show selected date range
              Consumer(
                builder: (context, ref, child) {
                  final dateRange = ref.watch(paySlipDateRangeProvider);

                  if (dateRange == null ||
                      dateRange['startDate'] == null ||
                      dateRange['endDate'] == null) {
                    return const SizedBox.shrink();
                  }

                  final startDate = DateFormat(
                    'dd MMM yyyy',
                  ).format(dateRange['startDate']!);
                  final endDate = DateFormat(
                    'dd MMM yyyy',
                  ).format(dateRange['endDate']!);

                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Date range on the left
                        OpenSansText(
                          '$startDate - $endDate',
                          fontSize: 14,
                          color: AppColors.viewColor,
                          fontWeight: FontWeight.w400,
                        ),

                        // Clear filters button on the right
                        TextButton(
                          onPressed: _clearFilters,
                          child: OpenSansText(
                            'clear filters',
                            fontSize: 12,
                            color: AppColors.viewColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              SizedBox(height: size.height * 0.01),
              Expanded(
                child: payslipsState.when(
                  loading:
                      () => const Center(child: CircularProgressIndicator()),
                  error:
                      (error, stackTrace) => Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            OpenSansText(
                              'Error loading payslips: ${error.toString()}',
                              color: AppColors.viewColor,
                              fontSize: 12,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed:
                                  () =>
                                      ref
                                          .read(payslipsProvider.notifier)
                                          .fetchPayslips(),
                              child: OpenSansText(
                                'Retry',
                                color: AppColors.viewColor,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                  data: (allPayslips) {
                    final paySlips = _applyFilters(allPayslips);

                    if (paySlips.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Show the no payslip found image
                            CustomPngImage(
                              imageName: 'nopayslip',
                              width: size.width * 0.4,
                              height: size.width * 0.4,
                            ),
                            // Show appropriate message based on filter/search state
                            if (_isFiltered || _isSearching) ...[
                              OpenSansText(
                                'No payslips for the selected date.',
                                fontSize: size.width * 0.040,
                                fontWeight: FontWeight.w400,
                                color: AppColors.textColor,
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(height: size.height * 0.01),
                              OpenSansText(
                                'Try selecting a different date',
                                fontSize: size.width * 0.040,
                                fontWeight: FontWeight.w400,
                                color: AppColors.viewColor,
                                textAlign: TextAlign.center,
                              ),
                            ] else ...[
                              OpenSansText(
                                'No payslips available',
                                fontSize: size.width * 0.04,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textColor,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ],
                        ),
                      );
                    }

                    // Group payslips by date when filtered
                    if (_isFiltered &&
                        (_startDate != null || _endDate != null)) {
                      return _buildGroupedPayslipsList(paySlips);
                    }

                    // Show regular list when not filtered
                    return ListView.builder(
                      padding: EdgeInsets.zero,
                      itemCount: paySlips.length,
                      itemBuilder: (context, index) {
                        final paySlip = paySlips[index];
                        return PaySlipListItem(
                          paySlip: paySlip,
                          formattedDate: _formatDate(paySlip.paymentDate),
                          onTap: () {},
                          //  => _showPaySlipPreview(paySlip),
                          onDownload:
                              () =>
                                  _handleDownload(paySlip.documentsOfRecordId),
                          isDownloading:
                              _downloadingPayslipId ==
                              paySlip.documentsOfRecordId,
                          showDateInTitle:
                              true, // Show date in title for regular list
                          onDismissed: () => _handlePayslipDismissed(paySlip),
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: CustomBottomNavigationBar(onTap: (index) {}),
      ),
    );
  }
}
