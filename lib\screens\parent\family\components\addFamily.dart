import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/commentFeild.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/custom_dropdown_container.dart';
import 'package:seawork/components/widget/fileUpload.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/employee/absence/providers/leaveRequestProviders.dart';
import 'package:seawork/screens/nursery/system/repository/systemRepositoryProvider.dart';
import 'package:seawork/screens/parent/family/models/familyModel.dart';
import 'package:seawork/screens/parent/family/models/uploadTos3ResponseModel.dart';
import 'package:seawork/screens/parent/family/provider/familyRepositoryProvider.dart';
import 'package:seawork/screens/parent/parent/constants/parentConstants.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/parent/family/myFamily.dart';
import 'package:seawork/screens/parent/family/provider/familyProvider.dart';
import 'package:dio/dio.dart';
import 'package:fluttertoast/fluttertoast.dart';

class AddFamilyScreen extends ConsumerStatefulWidget {
  const AddFamilyScreen({super.key});

  @override
  ConsumerState<AddFamilyScreen> createState() => _AddFamilyScreenState();
}

class _AddFamilyScreenState extends ConsumerState<AddFamilyScreen> {
  bool valid = false;

  @override
  Widget build(BuildContext context) {
    String? selectedStatus = ref.watch(maritalStatusProvider);
    bool isDropdownVisible = ref.watch(dropdownVisibilityProvider);
    bool showAdditionalOptions = ref.watch(showAdditionalOptionsProvider);
    bool showMarriedOptions = ref.watch(showMarriedOptionsProvider);
    PlatformFile? familyBookFile;
    PlatformFile? SEWAFile;
    UploadTos3? familyBookResponse;
    UploadTos3? sewaFileResponse;
    void showSnack(BuildContext context, String message) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }

    bool isFormFieldsFilled({
      required String familyName,
      required int? relationshipStatusId,
      required String? familyBookName,
      required String? residingInSharjah,
      required String? sewaDocumentName,
      required String? areaName,
      required String addressDetails,
    }) {
      if (familyName.isEmpty || relationshipStatusId == null) return false;
      if (relationshipStatusId != 2 && familyBookName == null) return false;
      if (residingInSharjah == null) return false;
      if (residingInSharjah == 'yes' &&
          (sewaDocumentName == null || areaName == null))
        return false;
      if (addressDetails.isEmpty) return false;
      return true;
    }

    // Used inside the onPressed to show feedback
    bool isFormValidWithFeedback({
      required BuildContext context,
      required String familyName,
      required int? relationshipStatusId,
      required String? familyBookName,
      required String? residingInSharjah,
      required String? sewaDocumentName,
      required String? areaName,
      required String addressDetails,
    }) {
      if (familyName.isEmpty || relationshipStatusId == null) {
        showSnack(context, "Please fill the required fields");
        return false;
      }
      if (relationshipStatusId != 2 && familyBookName == null) {
        showSnack(context, "Please upload family book");
        return false;
      }
      if (residingInSharjah == null) {
        showSnack(context, "Please select residing in Sharjah?");
        return false;
      }
      if (residingInSharjah == 'yes' &&
          (sewaDocumentName == null || areaName == null)) {
        showSnack(context, "Please upload SEWA document and select area");
        return false;
      }
      if (addressDetails.isEmpty) {
        showSnack(context, "Please enter address details");
        return false;
      }
      return true;
    }

    final familyName = ref.read(familyNameController).text;
    final addressDetails = ref.read(addressDetailsController).text;
    final residingInSharjah = ref.watch(residingInSharjahProvider);
    final addressArea = ref.read(addressDetailsController).text;
    final RealtionshipStatusId = ref.read(relationshipStatusIdProvider);
    final lat = ref.read(latitudeProvider);
    final lng = ref.read(longitudeProvider);
    final selectedArea = ref.read(selectedAreaNameProvider);
    final areaId = selectedArea?['id']; // dynamic, but usually int?
    final areaName = selectedArea?['name']; // dynamic, but usually String?
    final familyBookName = ref.watch(familyBookProvider);
    final sewaDocumentName = ref.watch(sewaDocumentProvider);

    final isFormFilled = isFormFieldsFilled(
      familyName: familyName,
      relationshipStatusId: RealtionshipStatusId,
      familyBookName: familyBookName,
      residingInSharjah: residingInSharjah,
      sewaDocumentName: sewaDocumentName,
      areaName: areaName,
      addressDetails: addressDetails,
    );

    final isSubmitting = ref.watch(isSubmittingProvider);
    final masterDataAsync = ref.watch(
      getAllMastersProvider((
        fields: "AreaName",
        pageNumber: 1,
        pageSize: 100,
        id: null,
        orderBy: null,
      )),
    );

    Future<dynamic> uploadFile(WidgetRef ref, PlatformFile file) async {
      try {
        final formData = FormData.fromMap({
          'file': await MultipartFile.fromFile(file.path!, filename: file.name),
        });

        final uploadResult = await ref.read(
          uploadTos3Provider(formData).future,
        );
        print('Upload result: ${uploadResult.fileKeyName}');

        Fluttertoast.showToast(
          msg: "Upload successful!",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );

        return uploadResult;
      } catch (e) {
        Fluttertoast.showToast(
          msg: "Upload failed: $e",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
        rethrow;
      }
    }

    Future<void> submitForm() async {
      final parentIdString = await PreferencesUtils.getString(
        PreferencesUtils.PARENT_ID,
      );
      final int parentId = int.tryParse(parentIdString ?? '') ?? 0;
      try {
        ref.read(isSubmittingProvider.notifier).state = true;
        // Save data to the provider in this file
        ref.read(familyNameProvider.notifier).state = familyName;

        // Set hasFamilyProvider from my_family.dart to true
        ref.read(hasFamilyProvider.notifier).state = true;

        // Ensure that displayMaritalStatusProvider has the right value
        final currentStatus = ref.read(maritalStatusProvider);
        if (currentStatus != 'Select marital status') {
          ref.read(displayMaritalStatusProvider.notifier).state = currentStatus;
        }

        FamilyModel family = FamilyModel(
          address: addressDetails,
          addressProofDocumentUpload: sewaDocumentName,
          annualIncome: 0,
          area: areaName,
          areaNameId: areaId,
          city: residingInSharjah,
          createdBy: 0,
          createdDate: '',
          emirateId: 0,
          familyBookAttachment: familyBookName,
          familyBookNo: '',
          familyNumber: 0,
          fmailyLocation: '{\"lat\":$lat,\"lng\":$lng}',
          isDeleted: false,
          isVerificationStarted: false,
          lastUpdatedBy: 0,
          lastUpdatedDate: '',
          lattitude: lat.toString(),
          longitude: lng.toString(),
          nickName: familyName,
          parentId: parentId,
          realtionshipStatusId: RealtionshipStatusId,
          currentTimeLineIndexNo: 2,
          profileReviewStage: 1,
        );

        await ref
            .read(addUpdateFamilyProvider(family).future)
            .then(
              (context) => {
                Fluttertoast.showToast(
                  msg: "Family added successfully",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.BOTTOM,
                  backgroundColor: AppColors.viewColor,
                  textColor: Colors.white,
                  fontSize: 16.0,
                ),
              },
            );

        // Navigate back to MyFamily screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => Myfamily()),
        );
      } catch (e) {
        ref.read(isSubmittingProvider.notifier).state = false;

        Fluttertoast.showToast(
          msg: "Failed to add family",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: AppColors.red,
          textColor: Colors.white,
          fontSize: 16.0,
        );
      } finally {
        ref.read(familyBookProvider.notifier).state = null;
        ref.read(familyNameProvider.notifier).state = null;
        ref.read(sewaDocumentProvider.notifier).state = null;
        ref.read(isSubmittingProvider.notifier).state = false;
        ref.invalidate(
          getFamilyDetailsByParentSpouseIdProvider((parentId: parentId)),
        );
      }
    }

    // Used to update the button color

    // Clear the controllers when the widget is built for the first time
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ref.read(familyNameController).text.isEmpty &&
          ref.read(familyNameProvider) != null) {
        ref.read(familyNameController).text =
            ref.read(familyNameProvider) ?? '';
      }
    });

    // Determine whether to show area fields based on marital status and residence
    bool showAreaFields =
        // Always show for Divorced or Widow/widower regardless of residence
        (selectedStatus == "Divorced" || selectedStatus == "Widow/widower") ||
        // For Single, only show if residing in Sharjah is Yes
        (selectedStatus == "Single" && residingInSharjah == 'Yes') ||
        // For Married, always show regardless of residence
        (selectedStatus == "Married");

    // Determine whether to show utility bill upload based on residence and status
    bool showUtilityBill =
        // For Single, Married - only show if residing in Sharjah is Yes
        (residingInSharjah == 'Yes' &&
            (selectedStatus == "Single" || selectedStatus == "Married")) ||
        // For Divorced, Widow/widower - always show regardless of residence
        (selectedStatus == "Divorced" || selectedStatus == "Widow/widower");

    // Determine whether to show family book upload
    bool showFamilyBook =
        // Show for Single, Divorced, Widow/widower, but NOT for Married
        (selectedStatus == "Single" ||
            selectedStatus == "Divorced" ||
            selectedStatus == "Widow/widower");

    return Center(
      child: Container(
        width: MediaQuery.of(context).size.width > 600 ? 600 : double.infinity,
        child: Scaffold(
          backgroundColor: AppColors.secondaryColor,
          appBar: CustomAppBar(title: 'Add Family'),
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextFieldWithHeading(
                            Heading: 'Enter family name',
                            hintText: 'Enter family name',
                            hasAsterisk: false,
                            fillColor: AppColors.whiteColor,
                            hintColor: AppColors.lightGreyshade,
                            hintStyle: AppColors.blackColor,
                            controller: ref.watch(familyNameController),
                          ),
                          const SizedBox(height: 24),
                          HeadingText(text: 'Marital status'),
                          const SizedBox(height: 8),
                          CustomDropdownContainer(
                            selectedStatus:
                                selectedStatus ?? 'Select marital status',
                            isDropdownVisible: isDropdownVisible,
                            options: const [
                              'Single',
                              'Married',
                              'Divorced',
                              'Widow/widower',
                            ],
                            onDropdownTap: () {
                              ref
                                  .read(dropdownVisibilityProvider.notifier)
                                  .state = !isDropdownVisible;
                            },
                            onSelectOption: (dynamic newValue) {
                              ref.read(maritalStatusProvider.notifier).state =
                                  newValue;
                              ref
                                  .read(dropdownVisibilityProvider.notifier)
                                  .state = false;

                              // Also update the display marital status provider
                              ref
                                  .read(displayMaritalStatusProvider.notifier)
                                  .state = newValue;

                              // Set the RelationshipStatusId based on selected status
                              int? relationshipStatusId;
                              switch (newValue) {
                                case 'Single':
                                  relationshipStatusId = 1;
                                  break;
                                case 'Married':
                                  relationshipStatusId = 2;
                                  break;
                                case 'Divorced':
                                  relationshipStatusId = 3;
                                  break;
                                case 'Widow/widower':
                                  relationshipStatusId = 4;
                                  break;
                                default:
                                  relationshipStatusId = null;
                              }
                              ref
                                  .read(relationshipStatusIdProvider.notifier)
                                  .state = relationshipStatusId;

                              // Set flags based on selected status
                              if (newValue == "Single") {
                                ref
                                    .read(
                                      showAdditionalOptionsProvider.notifier,
                                    )
                                    .state = true;
                                ref
                                    .read(showMarriedOptionsProvider.notifier)
                                    .state = false;
                              } else if (newValue == "Married") {
                                ref
                                    .read(showMarriedOptionsProvider.notifier)
                                    .state = true;
                                ref
                                    .read(
                                      showAdditionalOptionsProvider.notifier,
                                    )
                                    .state = false;
                              } else if (newValue == "Divorced" ||
                                  newValue == "Widow/widower") {
                                ref
                                    .read(
                                      showAdditionalOptionsProvider.notifier,
                                    )
                                    .state = true;
                                ref
                                    .read(showMarriedOptionsProvider.notifier)
                                    .state = true;
                              } else {
                                ref
                                    .read(
                                      showAdditionalOptionsProvider.notifier,
                                    )
                                    .state = false;
                                ref
                                    .read(showMarriedOptionsProvider.notifier)
                                    .state = false;
                                ref
                                    .read(additionalOptionProvider.notifier)
                                    .state = null;
                                ref
                                    .read(residingInSharjahProvider.notifier)
                                    .state = null;
                              }
                            },
                          ),

                          // Show fields based on marital status
                          if (selectedStatus == "Single" ||
                              selectedStatus == "Married" ||
                              selectedStatus == "Divorced" ||
                              selectedStatus == "Divorced" ||
                              selectedStatus == "Widow/widower") ...[
                            // Show family book only for Single, Divorced, and Widow/widower
                            if (showFamilyBook) ...[
                              const SizedBox(height: 24),
                              HeadingText(text: 'Upload family book'),
                              const SizedBox(height: 8),
                              FileUploadWidget(
                                onFileSelected: (
                                  fileName,
                                  fileSize,
                                  file,
                                ) async {
                                  setState(() {
                                    familyBookFile = file;
                                  });
                                  try {
                                    final response = await uploadFile(
                                      ref,
                                      file!,
                                    );
                                    setState(() {
                                      familyBookResponse = response;
                                      ref
                                          .read(familyBookProvider.notifier)
                                          .state = response.fileKeyName;
                                    });

                                    print('familyboofile: $file');
                                    print(
                                      'familyboofile file keyName: ${familyBookResponse?.fileKeyName}',
                                    );
                                  } catch (e) {
                                    print('Error uploading familyboofile: $e');
                                  }
                                },
                              ),
                            ],

                            // Show "Residing in Sharjah?" for all marital statuses
                            const SizedBox(height: 24),
                            HeadingText(text: 'Residing in Sharjah?'),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                // Custom compact radio button for Yes
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Radio<String>(
                                      value: 'Yes',
                                      groupValue: residingInSharjah,
                                      fillColor: WidgetStateProperty.all(
                                        AppColors.viewColor,
                                      ),
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      onChanged: (value) {
                                        ref
                                            .read(
                                              residingInSharjahProvider
                                                  .notifier,
                                            )
                                            .state = value;
                                      },
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        ref
                                            .read(
                                              residingInSharjahProvider
                                                  .notifier,
                                            )
                                            .state = 'Yes';
                                        ref
                                            .read(
                                              residingInSharjahProvider
                                                  .notifier,
                                            )
                                            .state = 'Yes';
                                      },
                                      child: Text('Yes'),
                                    ),
                                  ],
                                ),
                                const SizedBox(width: 16),
                                // Custom compact radio button for No
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Radio<String>(
                                      value: 'No',
                                      groupValue: residingInSharjah,
                                      fillColor: WidgetStateProperty.all(
                                        AppColors.viewColor,
                                      ),
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      onChanged: (value) {
                                        ref
                                            .read(
                                              residingInSharjahProvider
                                                  .notifier,
                                            )
                                            .state = value;
                                      },
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        ref
                                            .read(
                                              residingInSharjahProvider
                                                  .notifier,
                                            )
                                            .state = 'No';
                                        ref
                                            .read(
                                              residingInSharjahProvider
                                                  .notifier,
                                            )
                                            .state = 'No';
                                      },
                                      child: Text('No'),
                                    ),
                                  ],
                                ),
                              ],
                            ),

                            // Show Area Name field according to the requirements
                            if (showAreaFields) ...[
                              const SizedBox(height: 24),
                              HeadingText(text: 'Area Name'),
                              const SizedBox(height: 8),
                              masterDataAsync.when(
                                data: (data) {
                                  final areaList = data.areaNames;

                                  final areaNames =
                                      areaList!
                                          .map(
                                            (area) => {
                                              'id': area.id,
                                              'name': area.name,
                                            },
                                          )
                                          .toList();

                                  return CustomDropdownContainer(
                                    selectedStatus:
                                        ref.watch(
                                          selectedAreaNameProvider,
                                        )?['name'] ??
                                        'Select area',
                                    isDropdownVisible: ref.watch(
                                      areaDropdownVisibilityProvider,
                                    ),
                                    options: areaNames,
                                    onDropdownTap: () {
                                      ref
                                          .read(
                                            areaDropdownVisibilityProvider
                                                .notifier,
                                          )
                                          .state = !ref.read(
                                            areaDropdownVisibilityProvider,
                                          );
                                    },
                                    onSelectOption: (dynamic newValue) {
                                      ref
                                          .read(
                                            selectedAreaNameProvider.notifier,
                                          )
                                          .state = newValue;
                                      ref
                                          .read(
                                            areaDropdownVisibilityProvider
                                                .notifier,
                                          )
                                          .state = false;
                                    },
                                  );
                                },
                                loading:
                                    () => const CircularProgressIndicator(),
                                error:
                                    (e, st) =>
                                        Text('Error loading area names: $e'),
                              ),
                            ],

                            // Show utility bill upload based on the requirements
                            if (showUtilityBill) ...[
                              const SizedBox(height: 24),
                              HeadingText(
                                text:
                                    'Upload SEWA or Etihad water & Electricity bill',
                              ),
                              const SizedBox(height: 8),
                              FileUploadWidget(
                                onFileSelected: (
                                  fileName,
                                  fileSize,
                                  file,
                                ) async {
                                  setState(() {
                                    SEWAFile = file;
                                  });
                                  try {
                                    final response = await uploadFile(
                                      ref,
                                      file!,
                                    );
                                    setState(() {
                                      sewaFileResponse = response;
                                      ref
                                          .read(sewaDocumentProvider.notifier)
                                          .state = response.fileKeyName;
                                    });

                                    print('SEWAFile: $file');
                                    print(
                                      'sewaFileResponse: $sewaFileResponse',
                                    );
                                  } catch (e) {
                                    print('Error uploading SEWA file: $e');
                                  }
                                },
                              ),
                            ],

                            // Address details field shows for all selected marital statuses
                            const SizedBox(height: 24),
                            CommentFieldWithLocation(
                              heading: 'Address details',
                              controller: ref.watch(addressDetailsController),
                              onLocationFetched: (lat, lng) {
                                ref.read(latitudeProvider.notifier).state = lat;
                                ref.read(longitudeProvider.notifier).state =
                                    lng;
                              },
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
                // Always show the Save and Continue button
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            isFormFilled
                                ? AppColors.viewColor
                                : AppColors.lightGreyColorShade,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onPressed:
                          isSubmitting
                              ? null
                              : () async {
                                final isValid = isFormValidWithFeedback(
                                  context: context,
                                  familyName: familyName,
                                  relationshipStatusId: RealtionshipStatusId,
                                  familyBookName: familyBookName,
                                  residingInSharjah: residingInSharjah,
                                  sewaDocumentName: sewaDocumentName,
                                  areaName: areaName,
                                  addressDetails: addressDetails,
                                );

                                if (isValid) {
                                  await submitForm();
                                }
                              },
                      child:
                          isSubmitting
                              ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: AppColors.whiteColor,
                                  strokeWidth: 2,
                                ),
                              )
                              : DmSansText(
                                'Save and continue',
                                fontSize: 16,
                                color: AppColors.whiteColor,
                                fontWeight: FontWeight.w600,
                              ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
