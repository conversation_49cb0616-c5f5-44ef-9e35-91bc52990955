import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/text/dsSansText.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/commonWidgets/workflowindicator.dart';
import 'package:seawork/screens/service/ticket/models/ticketType.dart';
import 'package:seawork/screens/service/ticket/ticketDetailsSheet.dart';
import 'package:seawork/screens/service/ticket/providers/ticketProviders.dart';
import 'package:seawork/screens/service/ticket/models/ticketList.dart';
import 'package:seawork/screens/employee/absence/models/status.dart';

// Keep your existing method but adapt to take statusesAsync
Widget buildScrollableTabContent(
  BuildContext context,
  Ticket ticket,
  AsyncValue<List<TicketType>> ticketTypesAsync,
  AsyncValue<List<Status>> statusesAsync,
) {
  return ticketTypesAsync.when(
    data: (ticketTypes) {
      return statusesAsync.when(
        data: (statuses) {
          final ticketTypeName =
              ticketTypes
                  .where((type) => type.id == ticket.tickettypeId)
                  .firstOrNull
                  ?.name
                  .toString() ??
              'Unknown';

          final status = statuses.firstWhere(
            (s) => s.id == ticket.statusId,
            orElse:
                () => Status(
                  id: 0,
                  guid: '',
                  name: 'Unknown',
                  colour: '#000000',
                  sequence: 0,
                ),
          );

          return GestureDetector(
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder: (BuildContext context) {
                  return DraggableScrollableSheet(
                    initialChildSize: 0.70,
                    builder: (
                      BuildContext context,
                      ScrollController scrollController,
                    ) {
                      return TicketDetailsSheet(ticket: ticket);
                    },
                  );
                },
              );
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.boxshadow,
                    blurRadius: 9.6,
                    offset: Offset(0, 0),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: DMSans600Medium(
                            14,
                            'ID No : ${ticket.id}',
                            AppColors.blackColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        buildStatusBadge(ticket.statusId ?? 1),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        CustomSvgImage(imageName: "cardcalender"),
                        const SizedBox(width: 8),
                        Flexible(
                          child: OpenSansText(
                            formatDate(ticket.dateoccurred),
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: AppColors.headingColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 2),
                    const Divider(
                      color: AppColors.lightGreyColor3,
                      thickness: 0.4,
                      height: 16,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: OpenSans600Large(
                            12,
                            'Ticket type',
                            AppColors.blackColor,
                          ),
                        ),
                        Flexible(
                          child: OpenSans400Large(
                            12,
                            ticketTypeName,
                            AppColors.lightGreyColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    OpenSans600Large(12, 'Status', AppColors.blackColor),
                    const SizedBox(height: 8),
                    WorkflowIndicator(statusId: ticket.statusId ?? 1),
                    // OpenSansText(
                    //   status.name,
                    //   fontSize: 10,
                    //   fontWeight: FontWeight.w600,
                    // ),
                  ],
                ),
              ),
            ),
          );
        },
        loading: () => const SizedBox(),
        error: (_, __) => const SizedBox(),
      );
    },
    loading: () => const SizedBox(),
    error: (_, __) => const SizedBox(),
  );
}

// Keep your existing helper methods
Widget buildStatusBadge(int statusId) {
  final status = getStatusFromId(statusId);
  final backgroundColor = getStatusColor(statusId);

  return Container(
    width: 84,
    height: 22,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(25),
      color: backgroundColor,
    ),
    child: Center(
      child: DMSans600Small(
        10,
        status,
        AppColors.viewColor,
        fontWeight: FontWeight.w400,
      ),
    ),
  );
}

String getStatusFromId(int statusId) {
  if (statusId == 1) return 'Open';
  if (statusId == 9) return 'Closed';
  return 'In Progress';
}

Color getStatusColor(int statusId) {
  switch (statusId) {
    case 1: // New
      return AppColors.statuscolour;
    case 9: // Closed
      return AppColors.statuscolour2;
    default: // In Progress
      return AppColors.statuscolour3;
  }
}
