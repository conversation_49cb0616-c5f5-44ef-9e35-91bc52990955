import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/components/widget/customLoaderWithMessages.dart';
import 'package:seawork/components/widget/customMainTab.dart';
import 'package:seawork/components/widget/customStatusTabbar.dart';
import 'package:seawork/components/widget/customTabBarCard.dart';
import 'package:seawork/components/widget/noDataFound.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/employee/absence/components/customCreatedByMeLeaveTabBar.dart';
import 'package:seawork/screens/employee/absence/components/leaveSummaryCard.dart';
import 'package:seawork/screens/employee/absence/components/leaveTypeSearchBar.dart';
import 'package:seawork/screens/employee/absence/generateAndSavePDF.dart';
import 'package:seawork/screens/employee/absence/models/absenceRequest.dart';
import 'package:seawork/screens/employee/absence/models/planBalanceSummaryResponse.dart';
import 'package:seawork/screens/employee/absence/models/planBalances.dart';
import 'package:seawork/screens/employee/absence/models/userInfo.dart';
import 'package:seawork/screens/employee/absence/providers/absenceListNotifier.dart';
import 'package:seawork/screens/employee/absence/providers/absencesNotifier.dart';
import 'package:seawork/screens/employee/absence/providers/absencesProviders.dart';
import 'package:seawork/screens/employee/absence/providers/leaveRequestProviders.dart';
import 'package:seawork/screens/employee/absence/selectLeaveTypeScreen.dart';
import 'package:seawork/components/widget/customCompactCalendar.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/utils/util.dart';

class ApplyLeave extends ConsumerStatefulWidget {
  const ApplyLeave({super.key});

  @override
  ConsumerState<ApplyLeave> createState() => _ApplyLeaveState();
}

class _ApplyLeaveState extends ConsumerState<ApplyLeave> {
  int _selectedTabIndex = 0;
  Map<String, dynamic>? _selectedLeaveType;
  late FocusNode searchFocusNode;
  bool showSearchField = false;
  TextEditingController searchController = TextEditingController();
  final initialFetchDoneProvider = StateProvider<bool>((ref) => false);
  bool noMatchFound = false;
  Map<String, dynamic>? employeeDetails;
  String? personNumber;
  String? gender;
  String? religion;
  List<dynamic> roles = [];

  @override
  void initState() {
    super.initState();
    searchFocusNode = FocusNode();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      await _loadEmployeeDetails();

      ref.read(selectedLeaveTypeProvider.notifier).state = _selectedLeaveType;
      ref.read(isSubmittingProvider.notifier).state = false;
    });
  }

  Future<void> _loadEmployeeDetails() async {
    try {
      final jsonString = await PreferencesUtils.getEmployeeDetails();
      if (jsonString == null) return;

      final json = jsonDecode(jsonString);

      setState(() {
        employeeDetails = json;
        personNumber = json['PersonNumber'];
        gender = json['Gender'];
        religion = json['Religion'];
        roles = json['roles'] ?? [];
      });

      // Optional: only if you still want to store subfields separately
      await PreferencesUtils.setEmployeeSubFields(json);
    } catch (e) {
      print('Error decoding employee details: $e');
    }
  }

  void fetchAllAbsences() async {
    noMatchFound = false;
    ref.read(offsetProvider.notifier).state = 0;
    ref.read(awaitingListNotifierProvider.notifier).fetchData();
    ref.read(approvedListNotifierProvider.notifier).fetchData();
    ref.read(deniedListNotifierProvider.notifier).fetchData();
    ref.read(withdrawnListNotifierProvider.notifier).fetchData();
    ref.read(draftListNotifierProvider.notifier).fetchData();
  }

  void searchForLeaveType(Map<String, dynamic> selectedMap) {
    noMatchFound = false;
    ref.read(selectedSearchLeaveTypeProvider.notifier).state = selectedMap;
    ref.read(awaitingListNotifierProvider.notifier).fetchSearchAbsence();
    ref.read(approvedListNotifierProvider.notifier).fetchSearchAbsence();
    ref.read(deniedListNotifierProvider.notifier).fetchSearchAbsence();
    ref.read(withdrawnListNotifierProvider.notifier).fetchSearchAbsence();
    ref.read(draftListNotifierProvider.notifier).fetchSearchAbsence();
  }

  void onSearchNoMatchFound() {
    setState(() {
      noMatchFound = true;
    });
  }

  @override
  void dispose() {
    searchFocusNode.dispose();
    searchController.clear();
    super.dispose();
  }

  bool hasFetchedCounts = false;

  @override
  Widget build(BuildContext context) {
    print("Building ApplyLeave widget");
    final leaveTypeAsync = ref.watch(leaveTypeMapProvider);
    final leaveTypesRepo = ref.watch(leaveTypesRepositoryProvider);
    final awaitingCount = leaveTypesRepo.totalStatusCounts["AWAITING"] ?? 0;
    final approvedCount = leaveTypesRepo.totalStatusCounts["APPROVED"] ?? 0;
    final rejectedCount = leaveTypesRepo.totalStatusCounts["DENIED"] ?? 0;
    final withdrawnCount =
        leaveTypesRepo.totalStatusCounts["ORA_WITHDRAWN"] ?? 0;
    final draftCount = leaveTypesRepo.totalStatusCounts["SAVED"] ?? 0;
    bool _shouldUpdateSelectedLeaveType = true;

    final absencesAsync = ref.watch(awaitingListNotifierProvider);
    final isInitialFetchDone = ref.watch(initialFetchDoneProvider);
    bool isLoading = leaveTypeAsync.isLoading;
    String? personPlanEnrollmentId = ref.watch(personPlanEnrollmentIdProvider);
    double? AbsenceBalance;
    final planBalancesAsync = ref.watch(getPlanBalanceProvider);
    dynamic planBalanceSummary;

    print("Person Plan Enrollment ID: $personPlanEnrollmentId");
    final AsyncValue<List<PlanBalanceSummaryItemModel>>
    planBalanceSummaryAsync =
        personPlanEnrollmentId != null &&
                personPlanEnrollmentId.isNotEmpty &&
                personPlanEnrollmentId != "0"
            ? ref.watch(getPlanBalanceSummaryProvider(personPlanEnrollmentId))
            : const AsyncValue<List<PlanBalanceSummaryItemModel>>.data([]);

    final leaveTypeName =
        _selectedLeaveType != null
            ? _selectedLeaveType!['name']
            : 'Annual Leave';

    void handlePlanBalances(
      AsyncValue<List<PlanBalanceModel>> planBalancesAsync,
      String leaveType,
      Function(double?) onBalanceUpdated,
      Function(String?) onPersonPlanEnrollmentIdFound,
    ) {
      print("handlePlanBalances called with leaveType: $leaveType");
      planBalancesAsync.when(
        data: (planBalances) {
          print("Plan balances data received: ${planBalances.length} items");
          print("Plan balances: ${jsonEncode(planBalances)}");
          if (planBalances.isEmpty) {
            onBalanceUpdated(-1);
            print("No plan balances found for leave type: $leaveType");
            return;
          }

          final hasMatchingPlan = planBalances.any(
            (plan) => plan.planName == leaveType,
          );

          if (!hasMatchingPlan) {
            onBalanceUpdated(0);
            onPersonPlanEnrollmentIdFound("0");
            print("No matching plan found for leave type: $leaveType");
            return;
          }

          final matchingPlan = planBalances.firstWhere(
            (plan) => plan.planName == leaveType,
          );

          print("Matching plan found: ${jsonEncode(matchingPlan)}");

          final selectedBalance =
              matchingPlan.balanceAsOfBalanceCalculationDate;
          final absenceBalance =
              selectedBalance != null ? selectedBalance.ceilToDouble() : -1;

          onBalanceUpdated(absenceBalance as double?);
          onPersonPlanEnrollmentIdFound(
            matchingPlan.personPlanEnrollmentId.toString(),
          );
        },
        loading: () => const Center(child: CustomLoadingWidget()),
        error: (err, stack) {
          onBalanceUpdated(-1);
        },
      );
    }

    handlePlanBalances(
      planBalancesAsync,
      leaveTypeName,
      (balance) {
        if (!mounted) return;
        setState(() {
          AbsenceBalance = balance;
        });
      },
      (foundId) {
        if (!mounted) return;
        if (foundId != null) {
          Future.microtask(() {
            if (!mounted) return;
            ref.read(personPlanEnrollmentIdProvider.notifier).state = foundId;
          });
        }
      },
    );

    double totalLeaves = 0;
    double leaveBalance = 0;
    double leaveTaken = 0;
    if (personPlanEnrollmentId != null) {
      planBalanceSummaryAsync.when(
        data: (planBalanceSummary) {
          Future.microtask(() {
            ref
                .read(totalLeavesProvider.notifier)
                .updateTotalLeaves(planBalanceSummary);
            ref
                .read(leaveBalanceProvider.notifier)
                .updateLeaveBalance(planBalanceSummary);
            ref
                .read(leaveTakenProvider.notifier)
                .updateLeaveTaken(planBalanceSummary);
          });

          totalLeaves = ref.watch(totalLeavesProvider);
          leaveBalance = ref.watch(leaveBalanceProvider);
          leaveTaken = ref.watch(leaveTakenProvider);
        },
        loading: () {},
        error: (err, _) {},
      );
    }

    List<Map<String, dynamic>> leaveTypesData = [];

    leaveTypeAsync.when(
      data: (data) {
        leaveTypesData =
            data.where((e) => e['name'] != null && e['id'] != null).where((e) {
              final name = (e['name'] as String).toLowerCase();

              // Hide "Maternity Leave" for males
              if (name.contains('maternity') && gender?.toLowerCase() == 'm') {
                return false;
              }

              // Show "Paternity Leave" only for males
              if (name.contains('paternity') && gender?.toLowerCase() != 'm') {
                return false;
              }

              // Show "Hajj Leave" only for Muslims
              if (name.contains('hajj') &&
                  religion?.toLowerCase() != 'muslim') {
                return false;
              }

              return true;
            }).toList();

        if (leaveTypesData.isNotEmpty && _selectedLeaveType == null) {
          _selectedLeaveType = leaveTypesData.first;
        }
      },
      loading: () => '',
      error: (e, _) => '',
    );

    List<Map<String, dynamic>> leaveTypes = leaveTypesData;

    if (leaveTypes.isNotEmpty && !isInitialFetchDone) {
      Future.microtask(() {
        ref.read(initialFetchDoneProvider.notifier).state = true;
        ref.read(selectedLeaveTypeProvider.notifier).state = _selectedLeaveType;
      });
    }

    List<String> leaveTypeNames =
        leaveTypes
            .map((e) => capitalizeFirstWordOnly(e['name'].toString()))
            .toList();

    void selectLeaveType(Map<String, dynamic> selectedMap) {
      setState(() {
        _selectedLeaveType = selectedMap;
      });

      final selectedAbsenceTypeId = _selectedLeaveType!['id'];
      if (selectedAbsenceTypeId == null) return;

      ref.invalidate(
        getAbsenceProvider(
          AbsenceQueryParams(
            offset: 0,
            absenceTypeId: selectedAbsenceTypeId.toString(),
          ),
        ),
      );

      handlePlanBalances(
        planBalancesAsync,
        leaveTypeName,
        (balance) {
          setState(() {
            AbsenceBalance = balance;
          });
        },
        (foundId) {
          if (foundId != null) {
            Future.microtask(() {
              ref.read(personPlanEnrollmentIdProvider.notifier).state = foundId;
            });
          }
        },
      );

      if (_shouldUpdateSelectedLeaveType) {
        ref.read(selectedLeaveTypeProvider.notifier).state = _selectedLeaveType;
      }
    }

    Future<void> fetchTaskCountsForAllStatuses(WidgetRef ref) async {
      if (!mounted) return;
      final repo = ref.read(leaveTypesRepositoryProvider);

      final statuses = {
        'Awaiting': 'ASSIGNED',
        'Approved': 'APPROVED',
        'Rejected': 'REJECTED',
        'Withdrawn': 'WITHDRAWN',
      };

      for (var entry in statuses.entries) {
        try {
          final response = await repo.getTasksForAssignToMe(
            status: entry.value,
          );
          ref
              .read(taskCountsProvider.notifier)
              .setCount(entry.key, response.totalResults ?? 0);
        } catch (e) {
          print('Failed to fetch ${entry.key}: $e');
        }
      }
    }

    final CreatedByMetabs = [
      StatusTab(label: 'Pending'),
      StatusTab(label: 'Approved', count: approvedCount),
      StatusTab(label: 'Rejected', count: rejectedCount),
      StatusTab(label: 'Withdrawn', count: withdrawnCount),
      StatusTab(label: 'Draft'),
    ];
    // Trigger fetch logic when second tab is selected
    if (_selectedTabIndex == 1 && !hasFetchedCounts) {
      hasFetchedCounts = true;
      Future.microtask(() {
        if (!mounted) return;
        fetchTaskCountsForAllStatuses(ref);
      });
    }

    final taskCounts = ref.watch(taskCountsProvider);

    final AssignToMetabs = [
      StatusTab(label: 'Pending', count: taskCounts['Awaiting'] ?? 0),
      StatusTab(label: 'Approved'),
      StatusTab(label: 'Rejected'),
      StatusTab(label: 'Withdrawn', count: taskCounts['Withdrawn'] ?? 0),
    ];

    return Scaffold(
      extendBody: true,
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(title: 'Apply leave', showActionIcon: true),
      body: leaveTypeAsync.when(
        loading: () => Center(child: RotatingLoaderWithMessages()),
        error: (err, stack) => Center(child: OpenSansText("Error: $err")),
        data: (absencesData) {
          return Padding(
            padding: const EdgeInsets.all(18),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  LeaveSummaryCard(
                    leaveTypes: leaveTypes,
                    onUpdateLeaveTypeFlag: (bool value) {
                      setState(() {
                        _shouldUpdateSelectedLeaveType = value;
                      });
                    },
                    isLoading: isLoading,
                    leaveTypeNames: leaveTypeNames,
                    totalLeaves: totalLeaves,
                    leaveBalance: leaveBalance,
                    leaveTaken: leaveTaken,
                    selectLeaveType: selectLeaveType,
                    generateAndSavePDF: generateAndSavePDF,
                  ),
                  const SizedBox(height: 20),
                  // if (personId != "300000015297106")
                  CustomMainTabBar(
                    selectedIndex: _selectedTabIndex,
                    onTabSelected: (index) {
                      setState(() {
                        _selectedTabIndex = index;
                      });
                    },
                    tabs: [
                      TabItem(
                        label: 'Created by me',
                        badgeCount: awaitingCount,
                      ),
                      TabItem(
                        label: 'Assigned to me',
                        badgeCount: taskCounts['Awaiting'] ?? 0,
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  if (_selectedTabIndex == 0)
                    DefaultTabController(
                      length: 5,
                      child: Builder(
                        builder: (context) {
                          TabController tabController = DefaultTabController.of(
                            context,
                          );
                          return Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left: 6.0),
                                child: CustomStatusTabBar(
                                  controller: tabController,
                                  tabs: CreatedByMetabs,
                                ),
                              ),
                              const SizedBox(height: 5),
                              LeaveTypeSearchRow(
                                showSearchField: showSearchField,
                                searchController: searchController,
                                leaveTypes: leaveTypes,
                                onSearchNoMatchFound: onSearchNoMatchFound,
                                searchForLeaveType: searchForLeaveType,
                                fetchAllAbsences: fetchAllAbsences,
                                toggleSearch: () {
                                  setState(() {
                                    searchController.clear();
                                    showSearchField = !showSearchField;
                                    if (showSearchField) {
                                      Future.delayed(
                                        Duration(milliseconds: 100),
                                        () {
                                          searchFocusNode.requestFocus();
                                        },
                                      );
                                    }
                                  });
                                },
                                filterWidget: FilterBasedOnDates(
                                  selectedLeaveType: _selectedLeaveType,
                                  dateRangeProvider:
                                      applyLeaveDateRangeProvider,
                                ),
                                searchFocusNode: searchFocusNode,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 20,
                                  right: 20,
                                ),
                                child: Consumer(
                                  builder: (context, ref, child) {
                                    // final dateRange = ref.watch(
                                    //   dateRangeProvider,
                                    // );
                                    final dateRange = ref.watch(
                                      applyLeaveDateRangeProvider,
                                    );

                                    if (dateRange == null ||
                                        dateRange['startDate'] == null ||
                                        dateRange['endDate'] == null) {
                                      return const SizedBox.shrink();
                                    }

                                    final startDate = DateFormat(
                                      'dd MMM yyyy',
                                    ).format(dateRange['startDate']!);
                                    final endDate = DateFormat(
                                      'dd MMM yyyy',
                                    ).format(dateRange['endDate']!);

                                    return Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          // Date range on the left
                                          OpenSansText(
                                            '$startDate - $endDate',
                                            fontSize: 14,
                                            color: AppColors.viewColor,
                                            fontWeight: FontWeight.w400,
                                          ),

                                          // Clear filters button on the right
                                          TextButton(
                                            onPressed: () {
                                              // ref
                                              //     .read(
                                              //       dateRangeProvider.notifier,
                                              //     )
                                              //     .state = null;
                                              ref
                                                  .read(
                                                    applyLeaveDateRangeProvider
                                                        .notifier,
                                                  )
                                                  .state = null;

                                              fetchAllAbsences();
                                            },
                                            child: OpenSansText(
                                              'clear filters',
                                              fontSize: 12,
                                              color: AppColors.viewColor,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),

                              noMatchFound
                                  ? Padding(
                                    padding: const EdgeInsets.only(top: 10),
                                    child: noSearchResultWidget(context),
                                  )
                                  : Container(
                                    height: 400,
                                    child: TabBarView(
                                      children: [
                                        ref
                                            .watch(awaitingListNotifierProvider)
                                            .when(
                                              data: (awaitingAbsences) {
                                                return awaitingAbsences
                                                        .isNotEmpty
                                                    ? CreatedByMeLeaveTabBarWidget(
                                                      leaveTypes:
                                                          leaveTypeNames,
                                                      absenceData:
                                                          awaitingAbsences,
                                                      status: 'pending',
                                                      iconClicked:
                                                          'Apply Leave',
                                                      selectedTabIndex:
                                                          _selectedTabIndex,
                                                    )
                                                    : noDataFoundWidget(
                                                      context,
                                                    );
                                              },
                                              loading:
                                                  () => Center(
                                                    child:
                                                        CustomLoadingWidget(),
                                                  ),
                                              error:
                                                  (err, stack) =>
                                                      noDataFoundWidget(
                                                        context,
                                                      ),
                                            ),
                                        ref
                                            .watch(approvedListNotifierProvider)
                                            .when(
                                              data: (approvedAbsences) {
                                                return approvedAbsences
                                                        .isNotEmpty
                                                    ? CreatedByMeLeaveTabBarWidget(
                                                      leaveTypes:
                                                          leaveTypeNames,
                                                      absenceData:
                                                          approvedAbsences ??
                                                          [],
                                                      status: 'approved',
                                                      iconClicked:
                                                          'Apply Leave',
                                                      selectedTabIndex:
                                                          _selectedTabIndex,
                                                    )
                                                    : noDataFoundWidget(
                                                      context,
                                                    );
                                              },
                                              loading:
                                                  () => Center(
                                                    child:
                                                        CustomLoadingWidget(),
                                                  ),
                                              error:
                                                  (err, stack) =>
                                                      noDataFoundWidget(
                                                        context,
                                                      ),
                                            ),
                                        ref
                                            .watch(deniedListNotifierProvider)
                                            .when(
                                              data: (deniedAbsences) {
                                                return deniedAbsences.isNotEmpty
                                                    ? CreatedByMeLeaveTabBarWidget(
                                                      leaveTypes:
                                                          leaveTypeNames,
                                                      absenceData:
                                                          deniedAbsences ?? [],
                                                      status: 'rejected',
                                                      iconClicked:
                                                          'Apply Leave',
                                                      selectedTabIndex:
                                                          _selectedTabIndex,
                                                    )
                                                    : noDataFoundWidget(
                                                      context,
                                                    );
                                              },
                                              loading:
                                                  () => Center(
                                                    child:
                                                        CustomLoadingWidget(),
                                                  ),
                                              error:
                                                  (err, stack) =>
                                                      noDataFoundWidget(
                                                        context,
                                                      ),
                                            ),
                                        ref
                                            .watch(
                                              withdrawnListNotifierProvider,
                                            )
                                            .when(
                                              data: (withdrawnAbsences) {
                                                return withdrawnAbsences
                                                        .isNotEmpty
                                                    ? CreatedByMeLeaveTabBarWidget(
                                                      leaveTypes:
                                                          leaveTypeNames,
                                                      absenceData:
                                                          withdrawnAbsences ??
                                                          [],
                                                      status: 'withdrawn',
                                                      iconClicked:
                                                          'Apply Leave',
                                                      selectedTabIndex:
                                                          _selectedTabIndex,
                                                    )
                                                    : noDataFoundWidget(
                                                      context,
                                                    );
                                              },
                                              loading: () => loadingWidget(),
                                              error:
                                                  (err, stack) =>
                                                      noDataFoundWidget(
                                                        context,
                                                      ),
                                            ),
                                        ref
                                            .watch(draftListNotifierProvider)
                                            .when(
                                              data: (draftAbsences) {
                                                return draftAbsences.isNotEmpty
                                                    ? CreatedByMeLeaveTabBarWidget(
                                                      leaveTypes:
                                                          leaveTypeNames,
                                                      absenceData:
                                                          draftAbsences ?? [],
                                                      status: 'draft',
                                                      iconClicked:
                                                          'Apply Leave',
                                                      selectedTabIndex:
                                                          _selectedTabIndex,
                                                    )
                                                    : noDataFoundWidget(
                                                      context,
                                                    );
                                              },
                                              loading:
                                                  () => Center(
                                                    child:
                                                        CustomLoadingWidget(),
                                                  ),
                                              error:
                                                  (err, stack) =>
                                                      noDataFoundWidget(
                                                        context,
                                                      ),
                                            ),
                                      ],
                                    ),
                                  ),
                            ],
                          );
                        },
                      ),
                    ),
                  if (_selectedTabIndex == 1)
                    DefaultTabController(
                      length: 4,
                      child: Builder(
                        builder: (context) {
                          TabController tabController = DefaultTabController.of(
                            context,
                          );
                          return Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left: 6.0),
                                child: CustomStatusTabBar(
                                  controller: tabController,
                                  tabs: AssignToMetabs,
                                ),
                              ),
                              const SizedBox(height: 5),
                              LeaveTypeSearchRow(
                                showSearchField: showSearchField,
                                searchController: searchController,
                                leaveTypes: leaveTypes,
                                onSearchNoMatchFound: onSearchNoMatchFound,

                                searchForLeaveType: searchForLeaveType,
                                fetchAllAbsences: fetchAllAbsences,
                                toggleSearch: () {
                                  setState(() {
                                    searchController.clear();
                                    showSearchField = !showSearchField;
                                    if (showSearchField) {
                                      Future.delayed(
                                        Duration(milliseconds: 100),
                                        () {
                                          searchFocusNode.requestFocus();
                                        },
                                      );
                                    }
                                  });
                                },
                                filterWidget: FilterBasedOnDates(
                                  selectedLeaveType: _selectedLeaveType,
                                  dateRangeProvider:
                                      applyLeaveDateRangeProvider,
                                ),
                                searchFocusNode: searchFocusNode,
                              ),
                              Consumer(
                                builder: (context, ref, child) {
                                  // final dateRange = ref.watch(
                                  //   dateRangeProvider,
                                  // );
                                  final dateRange = ref.watch(
                                    applyLeaveDateRangeProvider,
                                  );

                                  if (dateRange == null ||
                                      dateRange['startDate'] == null ||
                                      dateRange['endDate'] == null) {
                                    return SizedBox.shrink();
                                  }

                                  final startDate = DateFormat(
                                    'dd MMM yyyy',
                                  ).format(dateRange['startDate']!);
                                  final endDate = DateFormat(
                                    'dd MMM yyyy',
                                  ).format(dateRange['endDate']!);

                                  return Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            OpenSansText(
                                              '$startDate - $endDate',
                                              fontSize: 14,
                                              color: AppColors.viewColor,
                                              fontWeight: FontWeight.w400,
                                            ),
                                            TextButton(
                                              onPressed: () {
                                                // ref
                                                //     .read(
                                                //       dateRangeProvider
                                                //           .notifier,
                                                //     )
                                                //     .state = null;
                                                ref
                                                    .read(
                                                      applyLeaveDateRangeProvider
                                                          .notifier,
                                                    )
                                                    .state = null;
                                                fetchAllAbsences();
                                              },
                                              child: OpenSansText(
                                                'clear filters',
                                                fontSize: 12,
                                                color: AppColors.viewColor,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                              noMatchFound
                                  ? noDataFoundWidget(context)
                                  : Container(
                                    height: 400, // Adjust height as needed
                                    child: TabBarView(
                                      children: [
                                        ref
                                            .watch(pendingTasksProvider)
                                            .when(
                                              loading:
                                                  () => const Center(
                                                    child:
                                                        CustomLoadingWidget(),
                                                  ),
                                              error:
                                                  (error, stack) => Center(
                                                    child: Text(
                                                      'Error: $error',
                                                    ),
                                                  ),
                                              data: (taskResponse) {
                                                final tasks =
                                                    taskResponse.items;
                                                return tasks.isNotEmpty
                                                    ? ReusableCard(
                                                      status: 'pending',
                                                      showDownloadButton: false,
                                                      tasks: taskResponse,
                                                      selectedTabIndex: 0,
                                                      iconClicked: 'Approval',
                                                      selectedStatus:
                                                          'ASSIGNED',
                                                      isVisible: true,
                                                      hideContainerForScreens:
                                                          false,
                                                    )
                                                    : noDataFoundWidget(
                                                      context,
                                                      message:
                                                          messageAssignedToMe,
                                                    );
                                              },
                                            ),

                                        // Approved tasks (COMPLETED)
                                        ref
                                            .watch(approvedTasksProvider)
                                            .when(
                                              loading:
                                                  () => const Center(
                                                    child:
                                                        CustomLoadingWidget(),
                                                  ),
                                              error:
                                                  (error, stack) => Center(
                                                    child: Text(
                                                      'Error: $error',
                                                    ),
                                                  ),
                                              data: (taskResponse) {
                                                final tasks =
                                                    taskResponse.items;
                                                return tasks.isNotEmpty
                                                    ? ReusableCard(
                                                      status: 'approved',
                                                      showDownloadButton: false,
                                                      tasks: taskResponse,
                                                      selectedTabIndex: 1,
                                                      iconClicked: 'Approval',
                                                      selectedStatus:
                                                          'COMPLETED',
                                                      isVisible: true,
                                                      hideContainerForScreens:
                                                          false,
                                                    )
                                                    : noDataFoundWidget(
                                                      context,
                                                      message:
                                                          messageAssignedToMe,
                                                    );
                                              },
                                            ),

                                        // Rejected tasks (SUSPENDED)
                                        ref
                                            .watch(rejectedTasksProvider)
                                            .when(
                                              loading:
                                                  () => const Center(
                                                    child:
                                                        CustomLoadingWidget(),
                                                  ),
                                              error:
                                                  (error, stack) => Center(
                                                    child: Text(
                                                      'Error: $error',
                                                    ),
                                                  ),
                                              data: (taskResponse) {
                                                final tasks =
                                                    taskResponse.items;
                                                return tasks.isNotEmpty
                                                    ? ReusableCard(
                                                      status: 'rejected',
                                                      showDownloadButton: false,
                                                      tasks: taskResponse,
                                                      selectedTabIndex: 2,
                                                      iconClicked: 'Approval',
                                                      selectedStatus:
                                                          'SUSPENDED',
                                                      isVisible: true,
                                                      hideContainerForScreens:
                                                          false,
                                                    )
                                                    : noDataFoundWidget(
                                                      context,
                                                      message:
                                                          messageAssignedToMe,
                                                    );
                                              },
                                            ),

                                        // Withdrawn tasks (WITHDRAWN)
                                        ref
                                            .watch(withdrawnTasksProvider)
                                            .when(
                                              loading:
                                                  () => const Center(
                                                    child:
                                                        CustomLoadingWidget(),
                                                  ),
                                              error:
                                                  (error, stack) => Center(
                                                    child: Text(
                                                      'Error: $error',
                                                    ),
                                                  ),
                                              data: (taskResponse) {
                                                final tasks =
                                                    taskResponse.items;
                                                return tasks.isNotEmpty
                                                    ? ReusableCard(
                                                      status: 'withdrawn',
                                                      showDownloadButton: false,
                                                      tasks: taskResponse,
                                                      selectedTabIndex: 3,
                                                      iconClicked: 'Approval',
                                                      selectedStatus:
                                                          'WITHDRAWN',
                                                      isVisible: true,
                                                      hideContainerForScreens:
                                                          false,
                                                    )
                                                    : noDataFoundWidget(
                                                      context,
                                                      message:
                                                          messageAssignedToMe,
                                                    );
                                              },
                                            ),
                                      ],
                                    ),
                                  ),
                            ],
                          );
                        },
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(bottom: 52.0),
        child: FloatingActionButton(
          onPressed: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              isDismissible: true,
              enableDrag: true,
              builder: (context) {
                return DraggableScrollableSheet(
                  initialChildSize: 0.6,
                  minChildSize: 0.3,
                  maxChildSize: 0.85,
                  expand: false,
                  builder: (context, scrollController) {
                    return ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20.0),
                        topRight: Radius.circular(20.0),
                      ),
                      child: LeaveTypesForNavigation(
                        leaveTypes: leaveTypeNames,
                        scrollController: scrollController,
                        isFromDashboard: true,
                      ),
                    );
                  },
                );
              },
            );
          },
          shape: const CircleBorder(),
          child: ClipOval(child: CustomSvgImage(imageName: "add_icon")),
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(onTap: (p0) {}),
    );
  }
}
