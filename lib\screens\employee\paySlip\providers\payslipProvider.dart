import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/screens/employee/paySlip/models/payslipModel.dart';
import 'package:seawork/screens/employee/paySlip/repository/payslipRepository.dart';
import 'package:open_file/open_file.dart';
import 'package:flutter/foundation.dart';
import 'package:seawork/data/module/networkModule.dart';

// Repository provider
final payslipRepositoryProvider = Provider<PayslipRepository>((ref) {
  return PayslipRepository(ref.read(dioProvider));
});

// Payslips state
enum PayslipsState { initial, loading, loaded, error }

class PayslipsNotifier extends StateNotifier<AsyncValue<List<PaySlip>>> {
  final PayslipRepository _repository;

  PayslipsNotifier(this._repository) : super(const AsyncValue.loading()) {
    fetchPayslips();
  }

  Future<void> fetchPayslips() async {
    try {
      state = const AsyncValue.loading();
      final payslips = await _repository.fetchPayslips();
      state = AsyncValue.data(payslips);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> downloadAndOpenPayslip(int documentsOfRecordId, context) async {
    try {
      final filePath = await _repository.downloadPayslipAttachment(
        documentsOfRecordId,
        context: context,
      );
      if (!kIsWeb && filePath.isNotEmpty) {
        await OpenFile.open(filePath);
      }
    } catch (e) {
      rethrow;
    }
  }
}

// Provider for payslips
final payslipsProvider =
    StateNotifierProvider<PayslipsNotifier, AsyncValue<List<PaySlip>>>((ref) {
      final repository = ref.watch(payslipRepositoryProvider);
      return PayslipsNotifier(repository);
    });

// Provider for download status
final downloadStatusProvider = StateProvider<AsyncValue<String>>((ref) {
  return const AsyncValue.data('');
});
