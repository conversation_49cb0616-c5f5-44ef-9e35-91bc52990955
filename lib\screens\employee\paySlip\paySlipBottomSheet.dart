import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/screens/employee/paySlip/models/payslipModel.dart';
import 'package:seawork/screens/employee/paySlip/providers/payslipProvider.dart';
import 'package:seawork/utils/style/colors.dart';

class PaySlipBottomSheet extends ConsumerWidget {
  final PaySlip paySlip;

  const PaySlipBottomSheet({Key? key, required this.paySlip}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(top: 12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Center(child: CustomSvgImage(imageName: "Rectangle_icon")),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.01,
          ), // 2% of screen height (~16px on an 800px screen)
          Padding(
            padding: EdgeInsets.only(left: 20, right: 20, top: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: DMSans600Medium(
                        20,
                        'Pay slip - ${paySlip.paymentDate}',
                        AppColors.blackColor,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        ref
                            .read(payslipsProvider.notifier)
                            .downloadAndOpenPayslip(
                              paySlip.documentsOfRecordId,
                               context,
                            );
                      },
                      child: CustomSvgImage(imageName: "download_icon"),
                    ),
                  ],
                ),

                OpenSans400Large(
                  12,
                  "View and download as pdf",
                  AppColors.lightBlack,
                ),
                SizedBox(height: 2),
                OpenSans400Large(
                  MediaQuery.of(context).size.width * 0.03,
                  "Payment Date: ${paySlip.paymentDate} (${paySlip.weekDay})",
                  AppColors.blackColor,
                ),
                SizedBox(height: 2),
                OpenSans400Large(
                  MediaQuery.of(context).size.width * 0.03,
                  "Amount: ${paySlip.amount} ${paySlip.currencyCode}",
                  AppColors.blackColor,
                ),
              ],
            ),
          ),

          SizedBox(
            height: MediaQuery.of(context).size.height * 0.00,
          ), // Small responsive gap

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 25.0),
            child: Center(
              child: Transform.translate(
                offset: Offset(0, -35),
                child: const CustomPngImages(
                  imageName: 'preview_file',
                  width: double.infinity,
                  height: 527,
                  boxFit: BoxFit.contain,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
