import 'package:flutter/material.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/utils/style/colors.dart';

class InfoPopupCard extends StatelessWidget {
  final String familyName;
  final String motherName;

  const InfoPopupCard({
    Key? key,
    required this.familyName,
    required this.motherName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppColors.transparentColor,
      child: Container(
        width: 220,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: const [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // const Icon(Icons.people, size: 18, color: Colors.blue),
                SvgImage14x14('assets/images/icon_people.svg'),
                const SizedBox(width: 6),
                Expanded(
                  child: OpenSans700Large(10, familyName, AppColors.blackColor),
                ),
              ],
            ),
            const SizedBox(height: 6),
            const Divider(height: 1, color: AppColors.blue),
            const SizedBox(height: 6),
            OpenSans400Large(
              10,
              "Mother’s name: $motherName",
              AppColors.blackColor,
            ),
          ],
        ),
      ),
    );
  }
}
