import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/image/renderNavigationPages.dart';

class SearchWidgetScreen extends StatefulWidget {
  final Function(String) onItemSelected;
  final Function(String) addRecentlyUsed;

  const SearchWidgetScreen({
    Key? key,
    required this.onItemSelected,
    required this.addRecentlyUsed,
  }) : super(key: key);

  @override
  _SearchWidgetScreenState createState() => _SearchWidgetScreenState();
}

class _SearchWidgetScreenState extends State<SearchWidgetScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, String>> _searchResults = [];
  bool _showCursor = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_performSearch);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch() {
    final query = _searchController.text;
    if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
      });
      return;
    }

    final allIcons = IconNavigationHelper.getAllIcons();
    final results =
        allIcons.where((icon) {
          return icon['label']?.toLowerCase().contains(query.toLowerCase()) ??
              false;
        }).toList();

    setState(() {
      _searchResults = results;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: AppColors.secondaryColor,
        title: Row(
          children: [
            IconButton(
              icon: const CustomSvgImage(imageName: 'appbackbutton'),
              onPressed: () => Navigator.pop(context),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Container(
                height: 37,
                decoration: BoxDecoration(
                  color: AppColors.inputfillColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  controller: _searchController,
                  autofocus: true,
                  showCursor: _showCursor,
                  cursorColor: AppColors.viewColor,
                  decoration: InputDecoration(
                    hintText: 'Search',
                    hintStyle: GoogleFonts.openSans(
                      color: AppColors.viewColor,
                      fontSize: 14,
                    ),
                    border: InputBorder.none,
                    suffixIcon: Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: CustomSvgImage(imageName: "search_icon"),
                    ),
                    suffixIconConstraints: BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                      maxWidth: 24,
                      maxHeight: 24,
                    ),
                    contentPadding: const EdgeInsets.only(
                      bottom: 12,
                      left: 8,
                      right: 8,
                    ),
                  ),
                  onTap: () {
                    setState(() {
                      _showCursor = true;
                    });
                  },
                ),
              ),
            ),
          ],
        ),
      ),

      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_searchController.text.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12.0,
                ),
                child: DmSansText(
                  'Search results',
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.dashboardheading,
                ),
              ),
            Expanded(
              child:
                  _searchController.text.isEmpty
                      ? Container()
                      : _searchResults.isNotEmpty
                      ? Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: GridView.builder(
                          padding: const EdgeInsets.only(top: 8),
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: _searchResults.length,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 4,
                                mainAxisSpacing: 16,
                                crossAxisSpacing: 8,
                                childAspectRatio: 0.85,
                              ),
                          itemBuilder: (context, index) {
                            return _buildIconWithLabel(
                              _searchResults[index]["path"]!,
                              _searchResults[index]["label"]!,
                              context,
                            );
                          },
                        ),
                      )
                      : Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomSvgImage(
                              imageName: "searchnodata",
                              width: 120,
                              height: 120,
                            ),
                            OpenSansText(
                              'No results just yet ',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.blackColor,
                            ),
                            SizedBox(height: 8),
                            OpenSansText(
                              'Try a different name or spelling ',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.blackColor,
                            ),
                          ],
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconWithLabel(
    String imagePath,
    String label,
    BuildContext context,
  ) {
    final String query = _searchController.text.toLowerCase();
    final String labelLower = label.toLowerCase();
    final int matchIndex = labelLower.indexOf(query);

    return Material(
      color: AppColors.transparentColor,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        splashColor: AppColors.microinteraction,
        highlightColor: AppColors.microinteraction,
        onTap: () {
          widget.addRecentlyUsed(label);
          // widget.onItemSelected(label);
          IconNavigationHelper.navigateToScreen(
            context: context,
            label: label,
            // onIconClicked: widget.onItemSelected,
          );
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              height: 40,
              width: 40,
              padding: const EdgeInsets.all(5),
              child: Center(
                child: SvgPicture.asset(imagePath, fit: BoxFit.contain),
              ),
            ),
            const SizedBox(height: 6),
            SizedBox(
              width: 72,
              child:
                  matchIndex >= 0
                      ? RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: label.substring(0, matchIndex),
                              style: GoogleFonts.openSans(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: AppColors.blackColor,
                              ),
                            ),
                            TextSpan(
                              text: label.substring(
                                matchIndex,
                                matchIndex + query.length,
                              ),
                              style: GoogleFonts.openSans(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: AppColors.bdaybannercolor1,
                              ),
                            ),
                            TextSpan(
                              text: label.substring(matchIndex + query.length),
                              style: GoogleFonts.openSans(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: AppColors.blackColor,
                              ),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      )
                      : OpenSansText(
                        label,
                        textAlign: TextAlign.center,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.blackColor,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        fontheight: 1.2,
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
