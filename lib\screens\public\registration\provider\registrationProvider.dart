import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/public/registration/repository/registrationRepository.dart';

// Provider for RegistrationRepository
final registrationRepositoryProvider = Provider<RegistrationRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return RegistrationRepository(dio, baseUrlAuthProvider);
});

// StateNotifier for sending OTP
final sendSignUpOtpProvider = StateNotifierProvider<
  SendSignUpOtpNotifier,
  AsyncValue<Map<String, dynamic>>
>((ref) {
  final repo = ref.watch(registrationRepositoryProvider);
  return SendSignUpOtpNotifier(repo);
});

class SendSignUpOtpNotifier
    extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final RegistrationRepository repository;
  SendSignUpOtpNotifier(this.repository) : super(const AsyncValue.data({}));

  Future<void> sendOtp(String phoneNumber) async {
    state = const AsyncValue.loading();
    try {
      final result = await repository.sendSignUpMobileOtpByPass(phoneNumber);
      state = AsyncValue.data(result);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}

// StateNotifier for verifying OTP
final verifySignUpOtpProvider = StateNotifierProvider<
  VerifySignUpOtpNotifier,
  AsyncValue<Map<String, dynamic>>
>((ref) {
  final repo = ref.watch(registrationRepositoryProvider);
  return VerifySignUpOtpNotifier(repo);
});

class VerifySignUpOtpNotifier
    extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final RegistrationRepository repository;
  VerifySignUpOtpNotifier(this.repository) : super(const AsyncValue.data({}));

  Future<void> verifyOtp(
    String phoneNumber,
    String requestId,
    String otp,
  ) async {
    state = const AsyncValue.loading();
    try {
      final result = await repository.verifySignUpMobileOtpByPass(
        phoneNumber: phoneNumber,
        requestId: requestId,
        otp: otp,
      );
      state = AsyncValue.data(result);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}

// StateNotifier for sending email OTP
final sendSignUpEmailOtpProvider = StateNotifierProvider<
  SendSignUpEmailOtpNotifier,
  AsyncValue<Map<String, dynamic>>
>((ref) {
  final repo = ref.watch(registrationRepositoryProvider);
  return SendSignUpEmailOtpNotifier(repo);
});

class SendSignUpEmailOtpNotifier
    extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final RegistrationRepository repository;
  SendSignUpEmailOtpNotifier(this.repository)
    : super(const AsyncValue.data({}));

  Future<void> sendOtp(String email) async {
    state = const AsyncValue.loading();
    try {
      final result = await repository.sendSignUpEmailOtpByPass(email);
      state = AsyncValue.data(result);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}

// StateNotifier for verifying email OTP
final verifySignUpEmailOtpProvider = StateNotifierProvider<
  VerifySignUpEmailOtpNotifier,
  AsyncValue<Map<String, dynamic>>
>((ref) {
  final repo = ref.watch(registrationRepositoryProvider);
  return VerifySignUpEmailOtpNotifier(repo);
});

class VerifySignUpEmailOtpNotifier
    extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final RegistrationRepository repository;
  VerifySignUpEmailOtpNotifier(this.repository)
    : super(const AsyncValue.data({}));

  Future<void> verifyOtp(String email, String otp) async {
    state = const AsyncValue.loading();
    try {
      final result = await repository.verifySignUpEmailOtp(
        email: email,
        otp: otp,
      );
      state = AsyncValue.data(result);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}

final signUpVerifyAllProvider = StateNotifierProvider<
  SignUpVerifyAllNotifier,
  AsyncValue<Map<String, dynamic>>
>((ref) {
  final repo = ref.watch(registrationRepositoryProvider);
  return SignUpVerifyAllNotifier(repo);
});

class SignUpVerifyAllNotifier
    extends StateNotifier<AsyncValue<Map<String, dynamic>>> {
  final RegistrationRepository repository;
  SignUpVerifyAllNotifier(this.repository) : super(const AsyncValue.data({}));

  Future<void> signUpVerifyAll({
    required String mobileNo,
    required String mobileOtp,
    required dynamic requestId,
    String? email,
    String? emailOtp,
    required bool isSignupWithoutEmail,
  }) async {
    state = const AsyncValue.loading();
    try {
      final result = await repository.signUpVerifyAll(
        mobileNo: mobileNo,
        mobileOtp: mobileOtp,
        requestId: requestId,
        email: email,
        emailOtp: emailOtp,
        isSignupWithoutEmail: isSignupWithoutEmail,
      );
      state = AsyncValue.data(result);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}
