import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:seawork/screens/parent/parent/models/parentEmployeeNameModel.dart';
import 'package:seawork/screens/parent/parent/repository/parentRepositoryProvider.dart';

// final workplaceSectorOptionsProvider = StateProvider<List<String>>((ref) => []);
final selectedEmployeeProvider = StateProvider<EmployerName?>((ref) => null);
final workplaceEmployeeListProvider = StateProvider<List<EmployerName>>(
  (ref) => [],
);
final employerTypeIdProvider = StateProvider<int>((ref) => -1);
final employerNameIdProvider = StateProvider<int>((ref) => -1);
final employerTypeProvider = StateProvider<String>((ref) => '');
final employerNameProvider = StateProvider<String>((ref) => '');
final letterOfEmploymentFileProvider = StateProvider<String?>((ref) => '');
final studentDocumentFileProvider = StateProvider<String?>((ref) => '');
final adressLatitudeProvider = StateProvider<String?>((ref) => '');
final adressLongitudeProvider = StateProvider<String?>((ref) => '');
final workLocationProvider = StateProvider<String>((ref) => '');
final nonEmployedStatusProvider = StateProvider<int?>((ref) => null);
final issuanceDateProvider = StateProvider<String>((ref) => '');
final addressProvider = StateProvider<String>((ref) => '');
final currentMenuProvider = StateProvider<int>((ref) => 0);

final allMastersProvider =
    AsyncNotifierProvider<AllMastersNotifier, Map<String, dynamic>?>(
      AllMastersNotifier.new,
    );

class AllMastersNotifier extends AsyncNotifier<Map<String, dynamic>?> {
  @override
  Future<Map<String, dynamic>?> build() async {
    final repo = ref.read(parentRepositoryProvider);

    // // Call API and save
    await repo.getAllMasters();

    // Load from SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    final jsonStr = prefs.getString('allMasters');
    if (jsonStr != null) {
      try {
        final decoded = json.decode(jsonStr) as Map<String, dynamic>;
        return decoded;
      } catch (e) {
        print('Error decoding: $e');
      }
    }
    return null;
  }
}

final emiratesAuthorityNamesProvider = Provider<List<String>>((ref) {
  final allMasters = ref.watch(allMastersProvider).value;
  final emirates = allMasters?['Emirates'] as List?;

  final names = emirates?.map((e) => e['Name'] as String).toList() ?? [];
  names.sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase()));
  return names;
});
