import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/letterRequest/components/request_letter_bottom_sheet.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_types_provider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/dashboard/mainDashboard/mainDashboard.dart';
import 'package:seawork/components/widget/customSubmitConfirmDialog.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/customWhite&BlueBorderElevatedButton.dart';
import 'package:seawork/components/widget/customlinkscreen.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customBlueButton.dart';

class Responsive {
  static double height(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.height * (percentage / 100);
  }

  static double width(BuildContext context, double percentage) {
    return MediaQuery.of(context).size.width * (percentage / 100);
  }
}

class SalaryInfoScreen extends ConsumerStatefulWidget {
  @override
  ConsumerState<SalaryInfoScreen> createState() => _SalaryInfoScreenState();
}

class _SalaryInfoScreenState extends ConsumerState<SalaryInfoScreen> {
  String? selectedType;
  bool _isDropdownOpen = false;
  final List<String> documentTypes = ['Salary transfer', 'Salary certificate'];
  final TextEditingController salaryTransferController =
      TextEditingController();
  final TextEditingController salaryCertificateController =
      TextEditingController();

  @override
  void dispose() {
    salaryTransferController.dispose();
    salaryCertificateController.dispose();
    super.dispose();
  }

  void _toggleDropdown() {
    setState(() {
      _isDropdownOpen = !_isDropdownOpen;
    });

    if (_isDropdownOpen) {
      _showRequestLetterBottomSheet();
    }
  }

  void _showRequestLetterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (context) {
        return Stack(
          children: [
            GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(color: Colors.transparent),
            ),
            DraggableScrollableSheet(
              initialChildSize: 0.2,
              minChildSize: 0.2,
              maxChildSize: 1.0,
              builder: (context, scrollController) {
                return ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20.0),
                    topRight: Radius.circular(20.0),
                  ),
                  child: RequestLetterBottomSheet('salary'),
                );
              },
            ),
          ],
        );
      },
    ).whenComplete(() {
      setState(() {
        _isDropdownOpen = false;
        ref.read(selectedOptionProvider.notifier).state = null;
      });
    });
  }

  void _showDocumentTypeBottomSheet() {
    setState(() => _isDropdownOpen = true);

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => _buildBottomSheetContent(),
    ).whenComplete(() => setState(() => _isDropdownOpen = false));
  }

  Widget _buildBottomSheetContent() {
    return ClipRRect(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      child: SizedBox(
        height: 161,
        child: Container(
          decoration: BoxDecoration(color: AppColors.whiteColor),
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: CustomSvgImage(
                  imageName: "Rectangle12231",
                  height: 5,
                  width: 30,
                ),
              ),
              // SizedBox(height: 14),
              SizedBox(
                height: Responsive.height(context, 1.2),
              ), // 1.2% of screen height
              // Column(
              //    children:
              //    documentTypes.map((type) {
              //    bool isSelected = selectedType == type;
              //     return GestureDetector(
              //       onTap: () {
              //         _updateSelectedType(type);
              //         Navigator.pop(context);
              //       },
              //       child: Padding(
              //         padding: isSelected
              //             ? EdgeInsets.symmetric(vertical: 4.0, horizontal: 0)
              //             : EdgeInsets.zero,
              //         child: Container(
              //           width: double.infinity,
              //           padding: EdgeInsets.all(12),
              //           alignment: Alignment.center,
              //           decoration: BoxDecoration(
              //             color: isSelected
              //                 ? AppColors.lightGreyColor2
              //                 : AppColors.transparentColor,
              //             borderRadius: BorderRadius.circular(4),
              //           ),
              //           child: OpenSansText(
              //             type,
              //             textAlign: TextAlign.center,
              //             // style: TextStyle(
              //             fontWeight:
              //                 isSelected ? FontWeight.w700 : FontWeight.w400,
              //             fontSize: 14,
              //             // fontFamily: 'Open Sans',
              //             color: AppColors.blackColor,
              //             // ),
              //           ),
              //         ),
              //       ),
              //     );
              //   }).toList(),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  void _updateSelectedType(String type) {
    setState(() {
      selectedType = type;
      _isDropdownOpen = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(title: 'Salary information letter'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: Responsive.height(context, 2.5)),
            HeadingText(text: 'Document type', hasAsterisk: true),
            SizedBox(height: Responsive.height(context, 1.0)),
            // GestureDetector(
            //   onTap: () {
            // showModalBottomSheet(
            //   context: context,
            //   isScrollControlled: true,
            //   backgroundColor: Colors.transparent,
            //   barrierColor: Colors.black.withOpacity(0.5),
            //   builder: (context) {
            //     return DraggableScrollableSheet(
            //       initialChildSize: 0.6,
            //       minChildSize: 0.3,
            //       maxChildSize: 1.0,
            //       builder: (context, scrollController) {
            //         return ClipRRect(
            //           borderRadius: const BorderRadius.only(
            //             topLeft: Radius.circular(20.0),
            //             topRight: Radius.circular(20.0),
            //           ),
            //              child: RequestLetterBottomSheet('salary'),
            //         );
            //       },
            //     );
            //   },
            // );
            // },
            GestureDetector(
              onTap: _toggleDropdown, // Use dedicated handler

              child: Container(
                width: double.infinity,
                height: 43,
                padding: EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.lightGreyColor2),
                  borderRadius: BorderRadius.circular(8),
                  color: AppColors.whiteColor,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    OpenSansText(
                      selectedType ?? "Select type",
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppColors.blackColor,
                    ),
                    CustomSvgImage(
                      imageName: _isDropdownOpen ? 'Arrowup' : 'ArrowLeft',
                      width: 24,
                      height: 24,
                    ),
                  ],
                ),
              ),
            ),
            // ),
            SizedBox(height: 24),
            if (selectedType == 'Salary transfer')
              _buildSalaryTransferFields(context),
            if (selectedType == 'Salary certificate')
              _buildSalaryCertificateFields(context),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min, // Prevents expansion
            children: [
              if (selectedType == 'Salary transfer')
                _buildTransferButtons(context),
              if (selectedType == 'Salary certificate')
                _buildCertificateButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  // Salary Transfer Fields
  List<FileModel> uploadedFiles = []; // Initialize the list
  // final TextEditingController salaryTransferLinkController =
  //     TextEditingController();

  Widget _buildSalaryTransferFields(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Attachment Label
        HeadingText(text: 'Attachments'),
        const SizedBox(height: 8),
        AttachmentField(uploadedFiles: [], onFilesChanged: (p0) => {}),
        SizedBox(height: 24),
        HeadingText(text: 'Link'),
        const SizedBox(height: 8),
        LinkInputList(),
      ],
    );
  }

  // Salary Certificate Fields

  Widget _buildSalaryCertificateFields(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Addressed To
        CustomTextFieldWithHeading(
          showSpacing: false,
          Heading: 'Addressed to',
          hintText: 'Enter',
          hasAsterisk: true,
          fillColor: Colors.white,
          hintColor: AppColors.lightGreyshade,
          hintStyle: AppColors.lightGreyshade,
        ),
        SizedBox(height: screenHeight * 0.03), // ~24px responsive

        HeadingText(text: 'Attachments'),
        const SizedBox(height: 8),
        AttachmentField(uploadedFiles: [], onFilesChanged: (p0) => {}),
        SizedBox(height: 24),
        HeadingText(text: 'Link'),
        const SizedBox(height: 8),
        LinkInputList(),
      ],
    );
  }

  //Elevated Buttons for view draft and Submit request  for salary tranfer
  Widget _buildTransferButtons(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return Column(
      children: [
        // View Draft Button
        ViewDraftButton(
          onPressed: () {
            // TODO: Implement draft viewing logic
          },
          text: 'View draft', // Pass the common text here
        ),

        SizedBox(height: screenHeight * 0.01), // ~8px responsive

        SubmitRequestButton(
          onPressed: () {
            showDialog(
              context: context,
              builder:
                  (context) => CustomSubmitConfirmDialog(
                    content1: 'Document',
                    type: 'document',
                    content3: "Salary transfer",
                    message:
                        "Your request for letter has been\nsubmitted and is ready to be reviewed.",
                    onClose: () => context.go('/dashboard'),
                    documentType: 'Salary transfer',
                    document: CustomSubmitConfirmDialog,
                  ),
            );
          },
          text: 'Submit request', // Reusing the text parameter
        ),

        SizedBox(height: screenHeight * 0.04), // ~32px responsive
      ],
    );
  }

  //Elevated Buttons for view draft and Submit request for Salarry certificate
  Widget _buildCertificateButtons(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return Column(
      children: [
        // View Draft Button
        ViewDraftButton(
          onPressed: () {
            // TODO: Implement draft viewing logic
          },
          text: 'View draft', // Pass the common text here
        ),

        SizedBox(height: screenHeight * 0.01), // ~8px responsive

        SubmitRequestButton(
          onPressed: () {
            showDialog(
              context: context,
              builder:
                  (context) => CustomSubmitConfirmDialog(
                    content1: 'Document',
                    type: 'document',
                    content3: "Salary certificate",
                    message:
                        "Your request for letter has been\nsubmitted and is ready to be reviewed.",
                    onClose: () => context.go('/dashboard'),
                    documentType: 'Salary certificate',
                    document: CustomSubmitConfirmDialog,
                  ),
            );
          },
          text: 'Submit request', // Reusing the text parameter
        ),

        SizedBox(height: screenHeight * 0.04), // ~32px responsive
      ],
    );
  }
}
