import 'package:flutter/material.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/components/widget/commentFeild.dart';
import 'package:seawork/components/widget/customBlueButton.dart';
import 'package:seawork/components/widget/fileUpload.dart';
import 'package:seawork/screens/parent/childProfile/components/popupConfirm.dart';
import 'package:seawork/screens/parent/kid/repository/medicalInfoProvidor.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/screens/parent/kid/models/kidMedicalConsentModel.dart';

class MedicalInfoCard extends ConsumerStatefulWidget {
  final int kidInfoId;
  final void Function(bool completed)? onComplete;

  const MedicalInfoCard({Key? key, required this.kidInfoId, this.onComplete})
    : super(key: key);

  @override
  ConsumerState<MedicalInfoCard> createState() => _MedicalInfoCardState();
}

class _MedicalInfoCardState extends ConsumerState<MedicalInfoCard> {
  final Map<String, bool?> _medicalConditions = {
    'Asthma': null,
    'Epilepsy': null,
    'Spasm': null,
    'Frequent fever': null,
    'Recurrent infection': null,
    'Diabetes': null,
  };

  final TextEditingController _otherController = TextEditingController();
  final TextEditingController _otherAllergyController = TextEditingController();

  bool? _foodAllergy;
  bool? _medicineAllergy;
  bool? _canJoinActivities;
  String? uploadedFileName;
  String? uploadedFileSize;
  bool _isSubmitting = false;

  Future<bool> _onWillPop() async {
    if (uploadedFileName != null) {
      final shouldPop = await confirmPopup(
        context: context,
        title: 'Leave this page?',
        content: 'Your changes are saved. You can return anytime.',
        positiveButtonText: 'Okay',
        negativeButtonText: 'Keep editing',
      );
      return shouldPop ?? false;
    }
    return true;
  }

  Future<void> _submitMedicalInfo() async {
    if (!_validateForm()) return;

    setState(() => _isSubmitting = true);

    try {
      final repository = ref.read(kidMedicalConsentRepositoryProvider);

      final consentData = KidMedicalConsent(
        id: 0,
        kidInfoId: 3134,
        isHaveAsthma: _medicalConditions['Asthma'],
        isHaveEpilepsy: _medicalConditions['Epilepsy'],
        isHaveSpasm: _medicalConditions['Spasm'],
        isHaveFrequentFever: _medicalConditions['Frequent fever'],
        isHaveRecurrentInfection: _medicalConditions['Recurrent infection'],
        isHaveDiabetes: _medicalConditions['Diabetes'],
        isHaveFoodAllergy: _foodAllergy,
        isHaveMedicineAllergy: _medicineAllergy,
        otherMedicalRemarks:
            _otherController.text.isEmpty ? null : _otherController.text,
        otherAllergyRemarks:
            _otherAllergyController.text.isEmpty
                ? null
                : _otherAllergyController.text,
        canPracticeActivities: _canJoinActivities,
        currentWizardNo: 3,
        profileReviewStage: 1,
        vaccinationCardUpload: uploadedFileName,
        createdBy: null,
        createdDate: null,
        lastUpdatedBy: null,
        lastUpdatedDate: null,
        sendForReview: null,
        isDeleted: null,
        kidInfo: null,
      );

      await repository.addKidMedicalConsent(consentData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Medical information saved successfully!'),
          ),
        );
        if (widget.onComplete != null) widget.onComplete!(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to save medical information: ${e.toString()}',
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  bool _validateForm() {
    final allRadiosFilled =
        !_medicalConditions.values.any((v) => v == null) &&
        _foodAllergy != null &&
        _medicineAllergy != null &&
        _canJoinActivities != null;
    final fileUploaded = uploadedFileName != null;

    if (!allRadiosFilled) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please answer all medical questions')),
      );
      return false;
    }

    if (!fileUploaded) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please upload vaccination certificate')),
      );
      return false;
    }

    return true;
  }

  Widget _buildYesNo(String label, bool? value, Function(bool) onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DMSans600Medium(14, label, AppColors.blackColor),
          Material(
            child: Row(
              children: [
                Radio<bool>(
                  value: true,
                  groupValue: value,
                  onChanged: (val) => onChanged(val!),
                  activeColor: AppColors.viewColor,
                ),
                const Text('Yes'),
                const SizedBox(width: 110),
                Radio<bool>(
                  value: false,
                  groupValue: value,
                  onChanged: (val) => onChanged(val!),
                  activeColor: AppColors.viewColor,
                ),
                const Text('No'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Container(
        color: AppColors.secondaryColor, // Set background color to white here
        child: Material(
          color: Colors.transparent, // Make Material widget transparent
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 18.0, vertical: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Medical conditions
                ..._medicalConditions.entries.map(
                  (entry) => _buildYesNo(entry.key, entry.value, (val) {
                    setState(() => _medicalConditions[entry.key] = val);
                  }),
                ),
                const SizedBox(height: 8),
                CommentField(
                  controller: _otherController,
                  heading: 'Others',
                  hintText: "Please specify",
                ),
                const SizedBox(height: 16),

                // Allergies
                OpenSans400Large(
                  14,
                  'Please confirm if the child is allergic to:',
                  AppColors.blackColor,
                ),
                const SizedBox(height: 12),
                _buildYesNo('Food', _foodAllergy, (val) {
                  setState(() => _foodAllergy = val);
                }),
                _buildYesNo('Medicine', _medicineAllergy, (val) {
                  setState(() => _medicineAllergy = val);
                }),
                const SizedBox(height: 12),
                CommentField(
                  controller: _otherAllergyController,
                  heading: 'Other allergies',
                  hintText: "Please specify",
                ),
                const SizedBox(height: 16),

                // Activities
                _buildYesNo(
                  'Can your child join sports & activities?',
                  _canJoinActivities,
                  (val) => setState(() => _canJoinActivities = val),
                ),
                const SizedBox(height: 20),
                DMSans600Medium(
                  14,
                  'Upload Vaccination certificate',
                  AppColors.blackColor,
                ),
                const SizedBox(height: 12),
                FileUploadWidget(
                  onFileSelected: (fileName, fileSize, _) {
                    setState(() {
                      uploadedFileName = fileName;
                      uploadedFileSize = fileSize;
                    });
                  },
                ),
                const SizedBox(height: 20),
                SubmitRequestButton(
                  onPressed: _isSubmitting ? () {} : _submitMedicalInfo,
                  text: _isSubmitting ? "Saving..." : "Save and continue",
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
