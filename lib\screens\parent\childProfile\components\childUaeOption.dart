import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/components/widget/fileUpload.dart';
import 'package:seawork/screens/ProfileSetup/components/textRecogntion.dart';
import 'package:seawork/screens/ProfileSetup/components/uploadPopup.dart';
import 'package:seawork/screens/parent/childProfile/components/childOtherOption.dart';
import 'package:seawork/screens/parent/childProfile/components/popupConfirm.dart';
import 'package:seawork/screens/parent/kid/repository/kidAddRepositorty.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/parent/kid/models/kidInfoModel.dart';

class ChildProfileCompleteData {
  final File? emiratesIdFrontFile;
  final File? emiratesIdBackFile;
  final File? profileImage;
  final String nameEn;
  final String nameAr;
  final String emiratesIdNumber;
  final String emiratesIdExpiryDate;
  final String gender;
  final String dateOfBirth;
  final String nationality;
  final String? passportNumber;
  final String? passportExpiryDate;
  final String? issuingAuthority;
  final String? familyBookNumber;
  final File? familyBookFile;
  final File? passportFile;
  final bool isDisabled;

  ChildProfileCompleteData({
    this.emiratesIdFrontFile,
    this.emiratesIdBackFile,
    this.profileImage,
    this.nameEn = '',
    this.nameAr = '',
    this.emiratesIdNumber = '',
    this.emiratesIdExpiryDate = '',
    this.gender = '',
    this.dateOfBirth = '',
    this.nationality = 'United Arab Emirates',
    this.passportNumber,
    this.passportExpiryDate,
    this.issuingAuthority,
    this.familyBookNumber,
    this.familyBookFile,
    this.passportFile,
    this.isDisabled = false,
  });

  KidInfo toKidInfo() {
    return KidInfo(
      id: 0,
      fullNameInEnglish: nameEn,
      fullNameInArabic: nameAr,
      gender: gender,
      dob: dateOfBirth,
      nationality: nationality,
      emiratesNo: emiratesIdNumber,
      emiratesExpiry: emiratesIdExpiryDate,
      emiratesDocumentUploadFront: emiratesIdFrontFile?.path.split('/').last,
      emiratesDocumentUploadBack: emiratesIdBackFile?.path.split('/').last,
      passportNo: passportNumber,
      passportExpiry: passportExpiryDate,
      passportDocumentUpload: passportFile?.path.split('/').last,
      issuedAuthority: issuingAuthority,
      familyNo: familyBookNumber,
      familyBookAttachment: familyBookFile?.path.split('/').last,
      profileDocumentUpload: profileImage?.path.split('/').last,
      isDisabled: isDisabled,
      isVerificationStarted: false,
      currentWizardNo: 2,
      profileReviewStage: 1,
      code: null,
      photo: null,
      familyId: null,
      passportDocumenttId: null,
      passportEmirateId: null,
      gradeId: null,
      passportBirthPlace: null,
      healthDocumentId: null,
      medicalConsent: null,
      createdBy: null,
      createdDate: null,
      lastUpdatedBy: null,
      lastUpdatedDate: null,
      isDeleted: null,
      sendForReview: null,
      verifiedBy: null,
      verificationStatus: null,
      allottedNursery: null,
      studentCode: null,
      studentStatus: null,
      studentStatusInArabic: null,
      verifiedOn: null,
    );
  }
}

final childProfileCompleteDataProvider =
    StateProvider<ChildProfileCompleteData>((ref) {
      return ChildProfileCompleteData();
    });

class ChildUaeOptionScreen extends ConsumerStatefulWidget {
  final ValueChanged<bool>? onComplete;

  const ChildUaeOptionScreen({Key? key, this.onComplete}) : super(key: key);

  @override
  ConsumerState<ChildUaeOptionScreen> createState() =>
      _ChildUaeOptionScreenState();
}

class _ChildUaeOptionScreenState extends ConsumerState<ChildUaeOptionScreen> {
  File? _uploadedFile;
  String _recognizedText = '';
  bool isCompleted = false;
  bool isExpanded = true;
  bool _isLoading = false;

  final TextEditingController _passportNumberController =
      TextEditingController();
  final TextEditingController _expiryDateController = TextEditingController();
  final TextEditingController _familyBookNumberController =
      TextEditingController();
  String? _selectedAuthority;

  late final Color _borderColor;
  late final BorderRadius _borderRadius;
  late final double _inputHeight;

  @override
  void initState() {
    super.initState();
    _borderColor = AppColors.calanderbordercolor;
    _borderRadius = BorderRadius.circular(8);
    _inputHeight = 43.0;

    _passportNumberController.text = ref.read(childPassportNumberProvider);
    _expiryDateController.text = ref.read(childPassportExpiryDateProvider);
    _selectedAuthority = ref.read(childPassportIssuingAuthorityProvider);
    _familyBookNumberController.text = ref.read(childFamilyBookNumberProvider);
    _uploadedFile = ref.read(childPassportFileProvider);

    _passportNumberController.addListener(_checkCompletion);
    _expiryDateController.addListener(_checkCompletion);
  }

  Future<bool> _onWillPop() async {
    if (_passportNumberController.text.isNotEmpty ||
        _expiryDateController.text.isNotEmpty ||
        _selectedAuthority != null ||
        ref.read(childBirthCertificateFileNameProvider) != null) {
      final shouldPop = await confirmPopup(
        context: context,
        title: 'Leave this page?',
        content: 'Your changes are saved. You can return anytime.',
        positiveButtonText: 'Okay',
        negativeButtonText: 'Keep editing',
      );
      return shouldPop ?? false;
    }
    return true;
  }

  void resetForm() {
    setState(() {
      _uploadedFile = null;
      _passportNumberController.clear();
      _expiryDateController.clear();
      _selectedAuthority = null;
      _familyBookNumberController.clear();
    });

    ref.read(childPassportNumberProvider.notifier).state = '';
    ref.read(childPassportExpiryDateProvider.notifier).state = '';
    ref.read(childPassportIssuingAuthorityProvider.notifier).state = null;
    ref.read(childFamilyBookNumberProvider.notifier).state = '';
    ref.read(childBirthCertificateFileNameProvider.notifier).state = null;
    ref.read(childBirthCertificateFileSizeProvider.notifier).state = null;
    ref.read(childPassportFileProvider.notifier).state = null;
  }

  @override
  void dispose() {
    _passportNumberController.removeListener(_checkCompletion);
    _expiryDateController.removeListener(_checkCompletion);
    _passportNumberController.dispose();
    _expiryDateController.dispose();
    _familyBookNumberController.dispose();
    super.dispose();
  }

  void _onFileProcessed(File file, String recognizedText) {
    final fileName = file.path.split('/').last.toLowerCase();
    final extension = fileName.split('.').last.toLowerCase();

    if (!['jpg', 'jpeg', 'png', 'pdf'].contains(extension)) {
      _showUnsupportedFormatPopup();
      return;
    }

    setState(() {
      _uploadedFile = file;
      _recognizedText = recognizedText;
    });

    ref.read(childPassportFileProvider.notifier).state = file;
    _populateFieldsFromRecognizedText(recognizedText);
  }

  void _showImageQualityPopup() {
    showDialog(
      context: context,
      builder:
          (context) => UploadPopup(
            description:
                'We couldn\'t extract all required information from the document. Please ensure the document is clear and all text is visible.',
            buttonText: 'Got it',
            onFileProcessed: _onFileProcessed,
          ),
    );
  }

  void _showUnsupportedFormatPopup() {
    showDialog(
      context: context,
      builder:
          (context) => UploadPopup(
            description:
                'Unsupported file format. Please upload a valid JPG, PNG, or PDF file.',
            buttonText: 'Got it',
            onFileProcessed: _onFileProcessed,
          ),
    );
  }

  void _populateFieldsFromRecognizedText(String text) {
    bool hasPassportNumber = false;
    bool hasExpiryDate = false;
    bool hasIssuingAuthority = false;

    final passportRegexList = [
      RegExp(
        r'(?:Passport\s*No|Passport\s*Number|P|N|Passport)[:\s]*([A-Za-z0-9]{6,12})',
        caseSensitive: false,
      ),
      RegExp(r'\b[A-Z][0-9]{7,9}\b'),
      RegExp(r'\b[A-Z]{2,3}[0-9]{6,8}\b'),
    ];

    for (final regex in passportRegexList) {
      final passportMatch = regex.firstMatch(text);
      if (passportMatch != null && passportMatch.groupCount >= 1) {
        final passportNumber = passportMatch.group(1)!.trim();
        if (passportNumber.length >= 6) {
          setState(() {
            _passportNumberController.text = passportNumber;
            hasPassportNumber = true;
          });
          ref.read(childPassportNumberProvider.notifier).state = passportNumber;
          break;
        }
      }
    }

    final expiryRegexList = [
      RegExp(
        r'(?:Date\s*of\s*Expiry|Expiry\s*Date|Exp|Bxpiry)[:\s]*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{2,4})',
        caseSensitive: false,
      ),
      RegExp(r'\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b'),
      RegExp(
        r'(?:Valid\s*until|Until|Date)[:\s]*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{2,4})',
        caseSensitive: false,
      ),
    ];

    for (final regex in expiryRegexList) {
      final expiryMatch = regex.firstMatch(text);
      if (expiryMatch != null && expiryMatch.groupCount >= 1) {
        String extractedExpiryDate = expiryMatch.group(1)!.trim();
        extractedExpiryDate = extractedExpiryDate.replaceAll('-', '/');
        final parts = extractedExpiryDate.split('/');
        if (parts.length == 3) {
          final day = parts[0].padLeft(2, '0');
          final month = parts[1].padLeft(2, '0');
          final year = parts[2].length == 2 ? '20${parts[2]}' : parts[2];

          setState(() {
            _expiryDateController.text = '$day/$month/$year';
            hasExpiryDate = true;
          });
          ref.read(childPassportExpiryDateProvider.notifier).state =
              '$day/$month/$year';
          break;
        }
      }
    }

    final issuingRegexList = [
      RegExp(
        r'(?:Issuing\s*Authority|Authority|Aathority)[:\s]*([A-Za-z\s]+)',
        caseSensitive: false,
      ),
      RegExp(
        r'(?:Place\s*of\s*Issue|Issued\s*at|Issued\s*in)[:\s]*([A-Za-z\s]+)',
        caseSensitive: false,
      ),
    ];

    for (final regex in issuingRegexList) {
      final issuingMatch = regex.firstMatch(text);
      if (issuingMatch != null && issuingMatch.groupCount >= 1) {
        final extractedAuthority = issuingMatch.group(1)!;
        final normalizedAuthority = _normalizeAuthority(extractedAuthority);

        if (normalizedAuthority.isNotEmpty) {
          setState(() {
            _selectedAuthority = normalizedAuthority;
            hasIssuingAuthority = true;
          });
          ref.read(childPassportIssuingAuthorityProvider.notifier).state =
              normalizedAuthority;
          break;
        }
      }
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final extractedFields = [
        hasPassportNumber,
        hasExpiryDate,
        hasIssuingAuthority,
      ];
      final extractedCount = extractedFields.where((e) => e).length;

      if (extractedCount == 0) {
        _showImageQualityPopup();
      } else if (extractedCount < 3) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'We found $extractedCount out of 3 fields. Please verify the information.',
            ),
            duration: Duration(seconds: 3),
          ),
        );
      }
    });

    _checkCompletion();
  }

  String _normalizeAuthority(String raw) {
    final cleaned = raw.toLowerCase().replaceAll(RegExp(r'[^a-z ]'), '').trim();

    final authorityMapping = {
      'abu dhabi': 'Abu Dhabi',
      'ajman': 'Ajman',
      'dubai': 'Dubai',
      'al ain': 'Al Ain',
      'sharjah': 'Sharjah',
      'ras al khaimah': 'Ras Al Khaimah',
      'fujairah': 'Fujairah',
      'umm al quwain': 'Umm Al Quwain',
    };

    for (final entry in authorityMapping.entries) {
      if (cleaned.contains(entry.key)) {
        return entry.value;
      }
    }

    return '';
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (picked != null) {
      final formattedDate =
          '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';
      setState(() {
        _expiryDateController.text = formattedDate;
      });
      ref.read(childPassportExpiryDateProvider.notifier).state = formattedDate;
      _checkCompletion();
    }
  }

  void _checkCompletion() {
    final passportValid = _passportNumberController.text.trim().isNotEmpty;
    final expiryValid = _expiryDateController.text.trim().isNotEmpty;
    final authorityValid = _selectedAuthority != null;

    final bool completed = passportValid && expiryValid && authorityValid;
    setState(() {
      isCompleted = completed;
    });

    if (widget.onComplete != null) {
      widget.onComplete!(completed);
    }
  }

  Future<void> _saveFormData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final completeData = ChildProfileCompleteData(
        emiratesIdFrontFile: ref.read(childEmiratesIdFileProvider),
        emiratesIdBackFile: ref.read(childEmiratesIdBackFileProvider),
        profileImage: ref.read(childProfileImageProvider),
        nameEn: ref.read(childNameEnProvider),
        nameAr: ref.read(childNameArProvider),
        emiratesIdNumber: ref.read(childIdNumberProvider),
        emiratesIdExpiryDate: formatDate(DateTime(2026, 6, 4)),
        gender: ref.read(childGenderProvider),
        dateOfBirth: formatDate(DateTime(2026, 6, 4)),
        nationality: ref.read(childNationalityProvider),
        passportNumber: _passportNumberController.text.trim(),
        passportExpiryDate: formatDate(DateTime(2026, 6, 4)),
        issuingAuthority: _selectedAuthority,
        familyBookNumber: _familyBookNumberController.text.trim(),
        familyBookFile: ref.read(childBirthCertificateFileProvider),
        passportFile: _uploadedFile,
        isDisabled: ref.read(childIsDisabledProvider) ?? false,
      );

      ref.read(childProfileCompleteDataProvider.notifier).state = completeData;

      final kidInfo = completeData.toKidInfo();
      final repository = ref.read(kidAddRepositoryProvider);

      final response = await repository.addKidConsent(kidInfo);

      if (widget.onComplete != null) {
        widget.onComplete!(true);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Child information saved successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save child information: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onBirthCertificateUploaded(
    String? fileName,
    String? fileSize,
    dynamic platformFile,
  ) {
    File? file;
    if (platformFile != null) {
      if (platformFile is File) {
        file = platformFile;
      } else if (platformFile.path != null) {
        file = File(platformFile.path);
      }
    }
    ref.read(childBirthCertificateFileNameProvider.notifier).state = fileName;
    ref.read(childBirthCertificateFileSizeProvider.notifier).state = fileSize;
    ref.read(childBirthCertificateFileProvider.notifier).state = file;
  }

  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'jpg':
      case 'jpeg':
      case 'png':
        return Icons.image;
      default:
        return Icons.insert_drive_file;
    }
  }

  InputDecoration _getInputDecoration(String hintText, {Widget? suffixIcon}) {
    return InputDecoration(
      hintText: hintText,
      border: OutlineInputBorder(
        borderRadius: _borderRadius,
        borderSide: BorderSide(color: _borderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: _borderRadius,
        borderSide: BorderSide(color: _borderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: _borderRadius,
        borderSide: BorderSide(color: _borderColor),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      suffixIcon: suffixIcon,
      isDense: true,
    );
  }

  Widget _buildFormFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DMSans600Medium(14, 'Passport Number', AppColors.blackColor),
        const SizedBox(height: 8),
        SizedBox(
          height: _inputHeight,
          child: TextField(
            controller: _passportNumberController,
            onChanged: (_) {
              ref.read(childPassportNumberProvider.notifier).state =
                  _passportNumberController.text;
              _checkCompletion();
            },
            decoration: _getInputDecoration('Enter your passport number'),
          ),
        ),
        const SizedBox(height: 16),
        DMSans600Medium(14, 'Expiry Date', AppColors.blackColor),
        const SizedBox(height: 8),
        SizedBox(
          height: _inputHeight,
          child: TextField(
            controller: _expiryDateController,
            onChanged: (_) {
              ref.read(childPassportExpiryDateProvider.notifier).state =
                  _expiryDateController.text;
              _checkCompletion();
            },
            decoration: _getInputDecoration(
              'DD/MM/YYYY',
              suffixIcon: Icon(Icons.calendar_today, size: 20),
            ),
            readOnly: true,
            onTap: () => _selectDate(context),
          ),
        ),
        const SizedBox(height: 16),
        // Issuing Authority Dropdown
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DMSans600Medium(14, 'Issuing Authority', AppColors.blackColor),
            const SizedBox(height: 8),
            Container(
              height: 43,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.calanderbordercolor),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedAuthority,
                  isExpanded: true,
                  icon: const Icon(Icons.keyboard_arrow_down),
                  hint: Text(
                    'Select',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.lightGreyColor4,
                      fontFamily: 'OpenSans',
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  items:
                      const [
                        'Abu Dhabi',
                        'Ajman',
                        'Dubai',
                        'Al Ain',
                        'Sharjah',
                        'Ras Al Khaimah',
                        'Fujairah',
                        'Umm Al Quwain',
                      ].map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            value,
                            style: const TextStyle(
                              fontSize: 14,
                              fontFamily: 'OpenSans',
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        );
                      }).toList(),
                  onChanged: (String? value) {
                    if (value != null) {
                      setState(() {
                        _selectedAuthority = value;
                      });
                      ref
                          .read(childPassportIssuingAuthorityProvider.notifier)
                          .state = value;
                      _checkCompletion();
                    }
                  },
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        DMSans600Medium(14, 'Family book number', AppColors.blackColor),
        const SizedBox(height: 8),
        SizedBox(
          height: _inputHeight,
          child: TextField(
            controller: _familyBookNumberController,
            onChanged: (value) {
              ref.read(childFamilyBookNumberProvider.notifier).state = value;
            },
            decoration: _getInputDecoration('Enter family book number'),
          ),
        ),
        const SizedBox(height: 16),
        DMSans600Medium(14, 'Upload birth certificate', AppColors.blackColor),
        const SizedBox(height: 8),
        FileUploadWidget(
          key: ValueKey(ref.read(childBirthCertificateFileNameProvider)),
          initialFileName: ref.read(childBirthCertificateFileNameProvider),
          initialFileSize: ref.read(childBirthCertificateFileSizeProvider),
          onFileSelected: _onBirthCertificateUploaded,
        ),
        const SizedBox(height: 20),
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed:
                (ref.read(childBirthCertificateFileNameProvider) == null ||
                        _isLoading)
                    ? null
                    : _saveFormData,
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 56),
              backgroundColor: AppColors.viewColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child:
                _isLoading
                    ? const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    )
                    : DmSansText(
                      'Save and Continue',
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.whiteColor,
                    ),
          ),
        ),
      ],
    );
  }

  Widget _buildOriginalFileUploadButton() {
    return Row(
      children: [
        Container(
          width: 80,
          height: 80,
          child: Padding(
            padding: const EdgeInsets.only(left: 8.0, top: 10, bottom: 10),
            child: Textrecognition(onFileProcessed: _onFileProcessed),
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                OpenSans400Large(14, 'Choose file', AppColors.lightGreyColor4),
                const SizedBox(height: 4),
                OpenSans400Large(
                  12,
                  'JPEG, JPG, PNG, PDF',
                  AppColors.lightGreyColor4,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadedFileView() {
    final fileName = _uploadedFile?.path.split('/').last ?? 'document';
    final fileSize =
        _uploadedFile != null
            ? '${(_uploadedFile!.lengthSync() / 1024).round()} kB'
            : '';

    return Row(
      children: [
        Container(
          width: 80,
          height: 80,
          padding: const EdgeInsets.all(16.0),
          child: Icon(_getFileIcon(fileName), color: AppColors.red, size: 40),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.blackColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  fileSize,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.mediumGreyColor,
                  ),
                ),
              ],
            ),
          ),
        ),
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.file_upload_outlined),
              onPressed: () {},
            ),
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                setState(() {
                  _uploadedFile = null;
                  _recognizedText = '';
                  _passportNumberController.clear();
                  _expiryDateController.clear();
                  _selectedAuthority = null;
                });
                ref.read(childPassportFileProvider.notifier).state = null;
                ref.read(childPassportNumberProvider.notifier).state = '';
                ref.read(childPassportExpiryDateProvider.notifier).state = '';
                ref.read(childPassportIssuingAuthorityProvider.notifier).state =
                    null;
                _checkCompletion();
              },
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Visibility(
        visible: isExpanded,
        child: Padding(
          padding: const EdgeInsets.all(18.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  DMSans600Medium(
                    14,
                    "Upload child's passport copy",
                    AppColors.blackColor,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.whiteColor,
                  borderRadius: _borderRadius,
                  border: Border.all(color: _borderColor),
                ),
                child:
                    _uploadedFile != null
                        ? _buildUploadedFileView()
                        : _buildOriginalFileUploadButton(),
              ),
              if (_uploadedFile != null) ...[
                const SizedBox(height: 20),
                _buildFormFields(),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

// Provider declarations
final childEmiratesIdFileProvider = StateProvider<File?>((ref) => null);
final childEmiratesIdBackFileProvider = StateProvider<File?>((ref) => null);
final childProfileImageProvider = StateProvider<File?>((ref) => null);
final childNameEnProvider = StateProvider<String>((ref) => '');
final childNameArProvider = StateProvider<String>((ref) => '');
final childIdNumberProvider = StateProvider<String>((ref) => '');
final childExpiryDateProvider = StateProvider<String>((ref) => '');
final childGenderProvider = StateProvider<String>((ref) => '');
final childDateOfBirthProvider = StateProvider<String>((ref) => '');
final childNationalityProvider = StateProvider<String>(
  (ref) => 'United Arab Emirates',
);
final childPassportNumberProvider = StateProvider<String>((ref) => '');
final childPassportExpiryDateProvider = StateProvider<String>((ref) => '');
final childPassportIssuingAuthorityProvider = StateProvider<String?>(
  (ref) => null,
);
final childFamilyBookNumberProvider = StateProvider<String>((ref) => '');
final childBirthCertificateFileNameProvider = StateProvider<String?>(
  (ref) => null,
);
final childBirthCertificateFileSizeProvider = StateProvider<String?>(
  (ref) => null,
);
final childBirthCertificateFileProvider = StateProvider<File?>((ref) => null);
final childPassportFileProvider = StateProvider<File?>((ref) => null);
final childIsDisabledProvider = StateProvider<bool?>((ref) => false);
