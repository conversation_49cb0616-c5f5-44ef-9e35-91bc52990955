import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/permission/permissions.dart';
import 'package:seawork/components/widget/dateFormatter.dart';
import 'package:seawork/screens/employee/absence/models/userInfo.dart';
import 'package:seawork/utils/style/colors.dart';

/// Model for each step in the stepper
class StepperItem {
  final String status; // 'submitted', 'approved', 'rejected', 'pending'
  final String name;
  final DateTime? date;
  final String reason;
  final String actionName; // 'Assigned', 'Comment Added', 'Approved', etc.
  final String? userIdentifier;

  StepperItem({
    required this.status,
    required this.name,
    this.date,
    this.userIdentifier,
    this.reason = '',
    required this.actionName,
  });
}

class DashedLinePlaceholder extends StatelessWidget {
  const DashedLinePlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    return OpenSansText(
      '----------',
      fontSize: 12,
      color: AppColors.lightBlack,
      fontWeight: FontWeight.w400,
    );
  }
}

/// Main Custom Stepper Widget
class CustomStepper extends StatelessWidget {
  final List<StepperItem> steps;
  final String? iconClicked;
  final bool isCreatedByMeOnly;
  CustomStepper({
    Key? key,
    required this.steps,
    required this.isCreatedByMeOnly,
    this.iconClicked,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isCompletedFlow =
        steps.isNotEmpty &&
        (steps.last.status.toLowerCase() == 'completed' &&
            steps.last.actionName == 'Task Completed - Approved');
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 14.0),
          child: DmSansText(
            'Status',
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: AppColors.blackColor,
          ),
        ),
        ...List.generate(steps.length, (index) {
          final item = steps[index];
          final isLast = index == steps.length - 1;
          return _StepperRow(
            isCreatedByMeOnly: isCreatedByMeOnly,
            item: item,
            iconClicked: iconClicked,
            isCompletedFlow: isCompletedFlow,
            isFirst: index == 0,
            isLast: isLast,
            statusLabel: _getStatusLabel(
              isCreatedByMeOnly, // Assuming isCreatedByMeOnly is false for this example
              item.actionName,
              item.status,
              isCompletedFlow,
            ),
          );
        }),
      ],
    );
  }

  String _getStatusLabel(
    bool isCreatedByMeOnly,
    String actionName,
    String status,
    bool isCompletedFlow,
  ) {
    if (actionName == 'Withdrawn' && status.toLowerCase() == 'withdrawn') {
      return 'Withdrawn';
    }
    if (actionName == "Rejected") {
      return 'Rejected by';
    }
    if (isCompletedFlow && status.toLowerCase() != "submitted") {
      return 'Approved by';
    }
    if (actionName == "Task Completed - Rejected") {
      return 'Rejected by';
    }
    if (actionName == "Assigned" &&
        status.toLowerCase() == "future participant") {
      return '';
    } else if (actionName == "Assigned" && status.toLowerCase() != "assigned") {
      if (isCreatedByMeOnly) {
        return 'Submitted on';
      } else {
        return 'Submitted by';
      }
    } else if (actionName == "Assigned") {
      return 'Assigned to';
    } else if (actionName == "Approved") {
      return 'Approved by';
    } else if (actionName == "Rejected") {
      return 'Rejected by';
    } else if (actionName == "Comment Added") {
      return 'Comment by';
    } else {
      return '';
    }
  }
}

class _StepperRow extends StatelessWidget {
  final StepperItem item;
  final bool isFirst;
  final bool isLast;
  final String statusLabel;
  final bool isCompletedFlow;
  final String? iconClicked;
  final bool isCreatedByMeOnly;
  const _StepperRow({
    required this.item,
    required this.isFirst,
    required this.isLast,
    required this.isCreatedByMeOnly,
    this.iconClicked,
    this.isCompletedFlow = false,
    required this.statusLabel,
  });

  @override
  Widget build(BuildContext context) {
    final bool isHighlighted =
        (isFirst && isCreatedByMeOnly && statusLabel == 'Submitted on') ||
        statusLabel == 'Withdrawn';
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              if (!isFirst)
                Container(width: 10, color: AppColors.inputfillColor),

              // Circle with icon overlaid on the line
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(width: 10, color: AppColors.inputfillColor),
                  _buildIndicator(
                    item.status,
                    item.actionName,
                    isCompletedFlow,
                  ),
                ],
              ),

              // Bottom part of the line
              if (!isLast)
                Container(
                  width: 10,
                  height: 40,
                  color: AppColors.inputfillColor,
                ),
            ],
          ),

          const SizedBox(width: 12),
          // Left: Status label and name
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 18.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (statusLabel.isNotEmpty)
                    OpenSansText(
                      statusLabel,
                      fontWeight:
                          isHighlighted ? FontWeight.w600 : FontWeight.w400,
                      fontSize: 12,
                      color:
                          isHighlighted
                              ? AppColors.blackColor
                              : AppColors.lightBlack,
                    ),
                  SizedBox(height: 4),
                  OpenSansText(
                    isHighlighted
                        ? ''
                        : statusLabel == 'Withdrawn'
                        ? 'Withdrawn'
                        : item.name,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                    color: AppColors.blackColor,
                  ),
                ],
              ),
            ),
          ),
          // Right: Date/time and reason
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 18.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment:
                    item.date != null
                        ? CrossAxisAlignment.start
                        : CrossAxisAlignment.end,
                children: [
                  item.date != null
                      ? OpenSansText(
                        formatDateAndTimeWithoutOrdinal(item.date!),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.lightBlack,
                        softWrap: false,
                        overflow: TextOverflow.visible,
                      )
                      : Padding(
                        padding: const EdgeInsets.only(right: 15),
                        child: DashedLinePlaceholder(),
                      ),

                  if (item.reason.isNotEmpty &&
                      item.actionName == 'Task Completed - Rejected')
                    Padding(
                      padding: const EdgeInsets.only(top: 6.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppColors.orangeColor,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Flexible(
                            child: OpenSansText(
                              item.reason,
                              fontSize: 12.0,
                              fontWeight: FontWeight.w400,
                              color: Colors.orange[700],
                              softWrap: false,
                              overflow: TextOverflow.visible,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicator(
    String status,
    String actionName,
    bool? isCompletedFlow,
  ) {
    Color color;
    Widget icon;
    if (actionName == "Task Completed - Rejected" || actionName == "Rejected") {
      color = AppColors.Orange; // Red for rejected
      icon = const Icon(Icons.close, color: Colors.white, size: 16);
    } else if (isCompletedFlow == true) {
      color = AppColors.checkmarkgreen; // Green for completed actions
      icon = const Icon(Icons.check, color: Colors.white, size: 16);
    } else
    // Pending status: assigned + assigned
    if (status.toLowerCase() == "assigned" && actionName == 'Assigned') {
      color = AppColors.goldenYellow; // Yellow/Amber
      icon = CustomPngImage(
        imageName: "pending",
        width: 8,
        height: 8,
        color: AppColors.whiteColor,
      );
    } else
    // Completed actions (Submitted, Approved, Comment Added)
    if (actionName == "Assigned" &&
            status.toLowerCase() != "future participant" ||
        actionName == "Approved" ||
        actionName == "Comment Added" ||
        actionName == "" && status.toLowerCase() == "completed") {
      color = AppColors.checkmarkgreen; // Green for completed actions
      icon = Icon(Icons.check, color: AppColors.whiteColor, size: 16);
    }
    // Rejected
    else if (actionName == "Rejected" || status.toLowerCase() == "COMPLETED") {
      color = AppColors.CustomStepperRed; // Red for rejected
      icon = Icon(Icons.close, color: AppColors.whiteColor, size: 16);
    } else if (isCreatedByMeOnly &&
        (status.toLowerCase() == "future participant" ||
            status.toLowerCase() == "assigned" ||
            status.toLowerCase() == "in_progress" ||
            actionName.isEmpty)) {
      color = AppColors.lightGreyshade; // Gray for future or unknown
      icon = const SizedBox.shrink();
    }
    // Future participants, pending, or null actionName
    else if (status.toLowerCase() == "future participant" ||
        status.toLowerCase() == "assigned" ||
        status.toLowerCase() == "in_progress" ||
        (status.toLowerCase() == "assigned" && actionName == 'Assigned') ||
        actionName.isEmpty) {
      color = AppColors.goldenYellow; // Yellow/Amber for pending
      icon = CustomPngImage(
        imageName: "pending",
        width: 8,
        height: 8,
        color: AppColors.whiteColor,
      );
    }
    // Any other state
    else {
      color = AppColors.lightGreyshade; // Gray for future or unknown
      icon = CustomPngImage(
        imageName: "withdrawn",
        width: 10,
        height: 10,
        color: AppColors.whiteColor,
      );
    }
    return Container(
      width: 30, // Slightly larger to act as background
      height: 30,
      decoration: BoxDecoration(
        color: AppColors.lightGreyshade, // Gray for future or unknown
        shape: BoxShape.circle,
        border: Border.fromBorderSide(
          BorderSide(color: AppColors.inputfillColor, width: 5),
        ),
      ),
      child: Center(
        child: Container(
          width: 22,
          height: 22,
          decoration: BoxDecoration(
            color: color, // Your dynamic inner color
            shape: BoxShape.circle,
          ),
          child: Center(child: icon),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return DateFormat('d MMMM, yyyy, hh:mm a').format(date);
  }
}

/// Combines task details and history items into stepper items
// List<StepperItem> combineTaskDetailsAndHistory(
//     dynamic taskDetails, List<dynamic>? historyItems) {
//   List<StepperItem> stepperItems = [];

//   // Add initial submission from task details if available
//   if (taskDetails != null &&
//       taskDetails.createdBy != null &&
//       taskDetails.createdDate != null) {
//     final initialDate = DateTime.tryParse(taskDetails.createdDate);
//     if (initialDate != null) {
//       stepperItems.add(StepperItem(
//         name: taskDetails.createdBy,
//         date: initialDate,
//         status: 'SUBMITTED',
//         reason: '',
//         actionName: 'Assigned',
//       ));
//     }
//   }

//   // If we have no history items, return with just the initial submission
//   if (historyItems == null || historyItems.isEmpty) {
//     return stepperItems;
//   }

//   // Process history items
//   for (var item in historyItems) {
//     final displayName = item.displayName ?? 'Unknown';
//     final updatedDate =
//         item.updatedDate != null ? DateTime.tryParse(item.updatedDate) : null;
//     final state = item.state ?? 'PENDING';
//     final reason = item.reason ?? '';

//     // Check if actionName exists using dynamic access to avoid NoSuchMethodError
//     String action = '';
//     try {
//       // Try to access the property using dynamic approach
//       action = item.actionName ?? '';
//     } catch (e) {
//       // If property doesn't exist, determine action based on state
//       if (state.toUpperCase() == 'ASSIGNED') {
//         action = 'Assigned';
//       } else if (state.toUpperCase() == 'OUTCOME_UPDATED') {
//         action = 'Approved';
//       } else if (state.toUpperCase() == 'REJECTED') {
//         action = 'Rejected';
//       } else if (state.toUpperCase() == 'FUTURE PARTICIPANT') {
//         action = '';
//       } else {
//         action = '';
//       }
//     }

//     // Skip future participants with no date (they go at the end)
//     if (updatedDate == null && state.toLowerCase() == 'future participant') {
//       continue;
//     }

//     stepperItems.add(StepperItem(
//       name: displayName,
//       date: updatedDate ?? null,
//       status: state,
//       reason: reason,
//       actionName: action,
//     ));
//   }

//   // Add future participants at the end
//   for (var item in historyItems) {
//     if (item.updatedDate == null &&
//         item.state?.toLowerCase() == 'future participant') {
//       String action = '';
//       try {
//         action = item.actionName ?? '';
//       } catch (e) {
//         // Default to empty for future participants
//       }

//       stepperItems.add(StepperItem(
//         name: item.displayName ?? 'Unknown',
//         date: null, // Just a placeholder since we don't show it
//         status: 'future participant',
//         reason: item.reason ?? '',
//         actionName: action,
//       ));
//     }
//   }

//   return stepperItems;
// }

List<StepperItem> combineTaskDetailsAndHistory(
  dynamic taskDetails,
  List<dynamic>? historyItems,
) {
  List<StepperItem> stepperItems = [];
  Map<String, StepperItem> latestEntries = {};

  // Add initial submission from task details if available
  if (taskDetails != null &&
      taskDetails.createdBy != null &&
      taskDetails.createdDate != null) {
    final initialDate = DateTime.tryParse(taskDetails.createdDate)?.toLocal();
    if (initialDate != null) {
      stepperItems.add(
        StepperItem(
          name: taskDetails.createdBy,
          date: initialDate,
          status: 'SUBMITTED',
          reason: '',
          actionName: 'Assigned',
        ),
      );
    }
  }

  // If we have no history items, return with just the initial submission
  if (historyItems == null || historyItems.isEmpty) {
    return stepperItems;
  }

  // Check for withdrawn condition
  final lastItem = historyItems.last;
  final isWithdrawn = (lastItem.state?.toUpperCase() == 'WITHDRAWN');

  if (isWithdrawn) {
    // Collect any intermediate OUTCOME_UPDATED states
    final outcomeUpdatedItems =
        historyItems
            .sublist(0, historyItems.length - 1)
            .where((item) => item.state?.toUpperCase() == 'OUTCOME_UPDATED')
            .map((item) {
              final updatedDate =
                  item.updatedDate != null
                      ? DateTime.tryParse(item.updatedDate)
                      : null;
              if (updatedDate == null) return null;

              return StepperItem(
                name: item.displayName ?? 'Unknown',
                date: updatedDate,
                status: item.state ?? 'PENDING',
                reason: item.reason ?? '',
                actionName: item.action ?? 'Approved',
              );
            })
            .whereType<StepperItem>()
            .toList();

    // Always add last WITHDRAWN item
    final lastDate =
        lastItem.updatedDate != null
            ? DateTime.tryParse(lastItem.updatedDate)
            : null;

    final withdrawnStep = StepperItem(
      name: lastItem.displayName ?? 'Unknown',
      date: lastDate,
      status: 'WITHDRAWN',
      reason: lastItem.reason ?? '',
      actionName: lastItem.action ?? 'Withdrawn',
    );

    // If intermediate OUTCOME_UPDATED exists, include them
    if (outcomeUpdatedItems.isNotEmpty) {
      stepperItems.addAll(outcomeUpdatedItems);
    }

    // Always add withdrawn at the end
    stepperItems.add(withdrawnStep);

    return stepperItems;
  }

  // Process history items
  for (var item in historyItems) {
    final displayName = item.displayName ?? 'Unknown';
    final updatedDate =
        item.updatedDate != null
            ? DateFormat(
              "yyyy-MM-dd HH:mm:ss",
            ).parse(item.updatedDate, true).toLocal()
            : null;
    final state = item.state ?? 'PENDING';
    final reason = item.reason ?? '';

    // Check if actionName exists using dynamic access to avoid NoSuchMethodError
    String action = '';
    try {
      // Try to access the property using dynamic approach
      action = item.action ?? '';
    } catch (e) {
      // If property doesn't exist, determine action based on state
      if (state.toUpperCase() == 'ASSIGNED') {
        action = 'Assigned';
      } else if (state.toUpperCase() == 'OUTCOME_UPDATED') {
        action = 'Approved';
      } else if (state.toUpperCase() == 'REJECTED') {
        action = 'Rejected';
      } else if (state.toUpperCase() == 'FUTURE PARTICIPANT') {
        action = '';
      } else if (action.toUpperCase() == 'TASK COMPLETED - REJECTED') {
        action = 'Rejected';
      } else {
        action = '';
      }
    }

    // Skip future participants with no date (they go at the end)
    if (updatedDate == null && state.toLowerCase() == 'future participant') {
      continue;
    }

    // Add the current item to the map, keeping only the latest for each userId
    if (updatedDate != null) {
      final userIdentifier =
          item.userIdentifier ??
          displayName; // Using userId or displayName as identifier
      if (!latestEntries.containsKey(userIdentifier) ||
          (latestEntries[userIdentifier]!.date != null &&
              updatedDate.isAfter(latestEntries[userIdentifier]!.date!))) {
        latestEntries[userIdentifier] = StepperItem(
          name: displayName,
          date: updatedDate,
          status: state,
          reason: reason,
          actionName: action,
        );
      }
    }
  }

  // Add all the latest entries to the final list
  stepperItems.addAll(latestEntries.values);

  // Add future participants at the end
  for (var item in historyItems) {
    if (item.updatedDate == null &&
        item.state?.toLowerCase() == 'future participant') {
      String action = '';
      try {
        action = item.actionName ?? '';
      } catch (e) {
        // Default to empty for future participants
      }

      stepperItems.add(
        StepperItem(
          name: item.displayName ?? 'Unknown',
          date: null, // Just a placeholder since we don't show it
          status: 'future participant',
          reason: item.reason ?? '',
          actionName: action,
        ),
      );
    }
  }

  return stepperItems;
}
