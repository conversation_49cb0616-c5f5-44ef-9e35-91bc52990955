import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customIcon.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/parent/childProfile/childDetails.dart';
import 'package:seawork/screens/parent/family/provider/familyRepositoryProvider.dart';
import 'package:seawork/screens/parent/parent/constants/parentConstants.dart';
import 'package:seawork/screens/parent/parent/repository/parentRepositoryProvider.dart';
import 'package:seawork/screens/parent/provider/comonProvider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/parent/family/provider/familyProvider.dart';
import 'package:go_router/go_router.dart';

class Myfamily extends ConsumerStatefulWidget {
  const Myfamily({Key? key}) : super(key: key);

  @override
  ConsumerState<Myfamily> createState() => _MyfamilyState();
}

class _MyfamilyState extends ConsumerState<Myfamily> {
  bool isExpanded = true;
  Map<int, bool> expandedMap = {};

  @override
  void initState() {
    super.initState();

    Future.microtask(() async {
      final parentIdString = await PreferencesUtils.getString(
        PreferencesUtils.PARENT_ID,
      );
      final parentId = int.tryParse(parentIdString ?? '') ?? 0;
      ref.read(parentIdProvider.notifier).state = parentId;
    });
  }

  @override
  Widget build(BuildContext context) {
    final parentId = ref.watch(parentIdProvider);
    int selectedIndex = ref.watch(selectedTabIndexProvider);
    bool hasFamily = ref.watch(hasFamilyProvider);
    String? familyName = ref.watch(familyNameProvider);
    String? maritalStatus = ref.watch(displayMaritalStatusProvider);
    final familyAsync = ref.watch(
      getFamilyDetailsByParentSpouseIdProvider((parentId: parentId)),
    );
    final parentAsync = ref.watch(
      getParentProfileDetailsByIdProvider((parentId: parentId)),
    );
    print('parentAsync: $parentAsync');
    print('familyAsync: $familyAsync');
    // Debug prints to help diagnose issues
    print("MyFamily - hasFamily: $hasFamily");
    print("MyFamily - familyName: $familyName");
    print("MyFamily - maritalStatus: $maritalStatus");

    return Center(
      child: Container(
        width: MediaQuery.of(context).size.width > 600 ? 600 : double.infinity,
        child: Scaffold(
          backgroundColor: AppColors.secondaryColor,
          appBar: CustomAppBar(title: 'My family'),
          body: SafeArea(
            child: familyAsync.when(
              data: (familyList) {
                return SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child:
                        familyList.isNotEmpty
                            ? buildFamilyColumn(familyList)
                            : Center(
                              child: Container(
                                height:
                                    MediaQuery.of(context).size.height - 200,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CustomPngImages(
                                      imageName: "nodataavailable",
                                    ),
                                  ],
                                ),
                              ),
                            ),
                  ),
                );
              },
              loading: () => Center(child: CustomLoadingWidget()),
              error: (err, stack) => Center(child: Text('Error: $err')),
            ),
          ),

          floatingActionButton: familyAsync.when(
            data: (familyList) {
              return parentAsync.when(
                data: (parent) {
                  final gender = parent?.gender?.toLowerCase();
                  final isFemale = gender == 'female';

                  // Check condition: exactly one family member AND female gender
                  if (familyList.length == 1 && isFemale) {
                    return null; // Disable button
                  }

                  // Otherwise show button
                  return GestureDetector(
                    onTap: () {
                      context.push('/add-family');
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 20.0),
                      child: CustomSvgImage(
                        imageName: "add_icon",
                        height: 63,
                        width: 63,
                      ),
                    ),
                  );
                },
                loading: () => null,
                error: (_, __) => null,
              );
            },
            loading: () => null,
            error: (_, __) => null,
          ),

          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
          bottomNavigationBar: CustomBottomNavigationBar(onTap: (p0) {}),
        ),
      ),
    );
  }

  String getRelationshipStatus(int id) {
    switch (id) {
      case 1:
        return "Single";
      case 2:
        return "Married";
      case 3:
        return "Divorced";
      case 4:
        return "Widow/Widower";
      default:
        return "Unknown";
    }
  }

  Widget buildFamilyColumn(List familyList) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          familyList.map<Widget>((family) {
            final String familyName = family.nickName;
            final int relationshipStatusId = family.realtionshipStatusId;
            bool showParentDetails = relationshipStatusId == 2;

            String detailsTitle =
                showParentDetails ? "Parent details" : "Child details";
            String detailsSubtitle =
                showParentDetails
                    ? "View or update spouse details"
                    : "View or update child details";

            String relationshipStatus = getRelationshipStatus(
              relationshipStatusId,
            );
            final int id =
                family.id; // assuming each family item has a unique 'id'
            final bool isExpanded = expandedMap[id] ?? false;

            return Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
              ),
              color: AppColors.whiteColor,
              child: Column(
                children: [
                  InkWell(
                    onTap: () {
                      setState(() {
                        expandedMap[id] = !(expandedMap[id] ?? false);
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          const SizedBox(width: 16),
                          CustomSvgImage(
                            imageName: "mainaccountavatar",
                            height: 39,
                            width: 39,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                DmSansText(
                                  familyName,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                ),
                                if (family.kidsIds.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4.0),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        // Children Count Text
                                        Expanded(
                                          child: Wrap(
                                            crossAxisAlignment:
                                                WrapCrossAlignment.center,
                                            children: [
                                              OpenSansText(
                                                "${family.kidsIds.length} children",
                                                fontSize: 12,
                                                fontWeight: FontWeight.w400,
                                                color: Colors.black,
                                              ),
                                              if (family.spouseName != null &&
                                                  family
                                                      .spouseName!
                                                      .isNotEmpty) ...[
                                                const SizedBox(width: 6),
                                                Container(
                                                  width: 5,
                                                  height: 5,
                                                  margin: const EdgeInsets.only(
                                                    right: 6,
                                                  ),
                                                  decoration:
                                                      const BoxDecoration(
                                                        color: Color(
                                                          0xFF0AC1CC,
                                                        ),
                                                        shape: BoxShape.circle,
                                                      ),
                                                ),
                                                OpenSansText(
                                                  "Mother: ${family.spouseName}",
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w400,
                                                  color: Colors.black,
                                                ),
                                              ],
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          Icon(
                            isExpanded
                                ? Icons.keyboard_arrow_up
                                : Icons.keyboard_arrow_down,
                            color: AppColors.viewColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (isExpanded)
                    Column(
                      children: [
                        Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 4.0,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.whiteColor,
                            borderRadius: BorderRadius.circular(8.0),
                            border: Border.all(color: AppColors.viewColor),
                          ),
                          child: ListTile(
                            leading: CustomIcon(
                              imagePath: "assets/images/accountavatar.svg",
                            ),
                            title: OpenSansText(
                              "Family details",
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                            subtitle: Text("View or update family details"),
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ChildDetailsScreen(),
                                ),
                              );
                            },
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 8.0,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.whiteColor,
                            borderRadius: BorderRadius.circular(8.0),
                            border: Border.all(color: AppColors.viewColor),
                          ),
                          child: ListTile(
                            leading: CustomIcon(
                              imagePath: "assets/images/accountavatar.svg",
                            ),
                            title: OpenSansText(
                              detailsTitle,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                            subtitle: Text(detailsSubtitle),
                            onTap: () {},
                          ),
                        ),
                        SizedBox(height: 8.0),
                      ],
                    ),
                ],
              ),
            );
          }).toList(),
    );
  }
}
