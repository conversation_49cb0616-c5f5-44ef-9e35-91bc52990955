name: seawork
description: "All in one app with parents, children, employee management, attendance, leave management, and payroll system."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.7+7

environment:
  sdk: ">=3.7.2 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  expandable_text: 2.3.0
  characters: ^1.1.0
  json_annotation: ^4.9.0
  flutter_emoji: ">= 2.0.0"
  flutter_html: ^3.0.0-alpha.3
  image_picker: ^1.1.2
  fluttertoast: ^8.2.12
  html: ^0.15.4
  google_mlkit_text_recognition: ^0.15.0 
  syncfusion_flutter_pdf: ^29.2.7+1

  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  clipboard: ^0.1.3
  # Base
  nb_utils:
    git: https://github.com/Rissmon/nb_utils
  intl: ^0.20.2
  google_fonts: ^6.2.1
  # Common
  url_launcher: ^6.2.1
  http: ^1.1.0

  # AUTHENTICATION
  sizer: ^3.0.5
  reactive_forms: ^18.0.0
  dio: ^5.3.2
  shared_preferences: ^2.3.4
  riverpod: ^2.6.1
  flutter_riverpod: ^2.6.1
  file_picker: ^10.1.9
  dotted_border: ^3.0.1
  auto_size_text: ^3.0.0
  file_saver: ^0.2.14
  base64: ^1.0.0
  pin_code_fields: ^8.0.1
  badges: ^3.1.1
  flutter_native_splash: ^2.3.1
  injectable: ^2.4.2
  freezed: ^3.0.6
  flutter_svg: ^2.0.7
  # firebase_auth: ^5.3.0
  # firebase_core: ^3.5.0
  path_provider: ^2.1.1
  path: ^1.8.3
  firebase_messaging: ^15.2.9
  flutter_local_notifications: ^19.2.1
  # scroll_to_index: ^3.0.1

  #icons
  firebase_crashlytics: ^4.1.2
  universal_html: ^2.2.4
  pinput: ^5.0.1
  uuid: ^4.2.2

  firebase_analytics: ^11.4.6
  flutter_timezone: ^4.1.1
  open_file: ^3.5.10
  pdf: ^3.11.3
  excel: ^4.0.6
  archive: ^3.6.1
  app_links: ^6.4.0
  freezed_annotation: ^3.0.0
  translator: ^1.0.3+1
  flutter_localizations:
    sdk: flutter
  jwt_decoder: ^2.0.1
  flutter_gen: ^5.10.0
  geocoding: ^4.0.0
  geolocator: ^14.0.1
  transparent_image: ^2.0.1
  flutter_image_compress: ^2.4.0
  go_router: ^14.1.1
  flutter_custom_tabs: ^1.0.4
dev_dependencies:
  injectable_generator:
  build_runner: ^2.4.11
  json_serializable: ^6.7.1
  flutter_launcher_icons: ^0.14.3

dependency_overrides:
#dart run flutter_launcher_icons
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "images/app_icon.png"
  remove_alpha_ios: true
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/fonts/
    - assets/images/

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
          weight: 400
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
  # Enable automatic file generation
  # generate: true # Add this line
