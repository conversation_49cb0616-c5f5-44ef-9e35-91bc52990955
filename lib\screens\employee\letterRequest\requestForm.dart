import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/screens/employee/absence/models/userInfo.dart';
import 'package:seawork/screens/employee/letterRequest/components/requestForm_content.dart';
import 'package:seawork/screens/employee/letterRequest/components/request_letterDetailScreen_bottom_sheet.dart';
import 'package:seawork/screens/employee/letterRequest/components/submitButton_requestForm.dart';
import 'package:seawork/screens/employee/letterRequest/components/viewDraftButton_requestForm.dart';
import 'package:seawork/screens/employee/letterRequest/models/letter_request_types_model.dart';
import 'package:seawork/screens/employee/letterRequest/providers/link_list_provider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/letterRequest/noc.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/utils/util.dart';

// StateNotifier to manage the form state
class RequestDetailNotifier extends StateNotifier<RequestDetailState> {
  RequestDetailNotifier(LetterRequestTypesModel letterRequestType)
    : super(RequestDetailState(letterRequestType: letterRequestType));

  void updateRequestType(LetterRequestTypesModel? newType) {
    state = state.copyWith(letterRequestType: newType);
  }
}

// State class to hold the form state
class RequestDetailState {
  final LetterRequestTypesModel letterRequestType;

  RequestDetailState({required this.letterRequestType});

  RequestDetailState copyWith({LetterRequestTypesModel? letterRequestType}) {
    return RequestDetailState(
      letterRequestType: letterRequestType ?? this.letterRequestType,
    );
  }
}

List<FileModel> uploadedFiles = [];

void _updateUploadedFiles(List<FileModel> files) {
  uploadedFiles = files;
}

// Provider for the RequestDetailNotifier
final requestDetailProvider = StateNotifierProvider.family<
  RequestDetailNotifier,
  RequestDetailState,
  LetterRequestTypesModel
>((ref, letterRequestType) => RequestDetailNotifier(letterRequestType));

class RequestDetail extends ConsumerStatefulWidget {
  final LetterRequestTypesModel letterRequestType;
  final DocumentContext originContext;
  RequestDetail({
    Key? key,
    required this.letterRequestType,
    required this.originContext,
  }) : super(key: key);

  @override
  _RequestDetailState createState() => _RequestDetailState();
}

class _RequestDetailState extends ConsumerState<RequestDetail> {
  // Add a unique key for the form to force rebuild when document type changes
  Key _formKey = UniqueKey();

  final TextEditingController accountNumberController = TextEditingController();
  final TextEditingController ibanNumberController = TextEditingController();
  final TextEditingController bankNameController = TextEditingController();
  final TextEditingController branchNameController = TextEditingController();
  final TextEditingController swiftCodeController = TextEditingController();
  final TextEditingController addressedToController = TextEditingController();
  final TextEditingController nameInEnglishController = TextEditingController();
  final TextEditingController nameInArabicController = TextEditingController();
  final TextEditingController titleInEnglishController =
      TextEditingController();
  final TextEditingController titleInArabicController = TextEditingController();
  final TextEditingController mobileNumberController = TextEditingController();
  final TextEditingController directNumberController = TextEditingController();
  final TextEditingController remarksInEnglishController =
      TextEditingController();
  final TextEditingController remarksInArabicController =
      TextEditingController();
  final TextEditingController linkController = TextEditingController();
  final TextEditingController travelStartDateController =
      TextEditingController();
  final TextEditingController travelEndDateController = TextEditingController();
  // final TextEditingController salaryCertificateDateController = TextEditingController();

  List<FileModel> _uploadedFiles = [];
  List<Map<String, dynamic>> links = [];
  bool allRequiredFieldsFilled = false;
  bool isDropdownOpen = false;
  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      ref
          .read(requestDetailProvider(widget.letterRequestType).notifier)
          .updateRequestType(widget.letterRequestType);
      _addListenersToControllers(ref);
    });
  }

  void _addListenersToControllers(WidgetRef ref) {
    accountNumberController.addListener(_checkRequiredFields);
    ibanNumberController.addListener(_checkRequiredFields);
    bankNameController.addListener(_checkRequiredFields);
    branchNameController.addListener(_checkRequiredFields);
    addressedToController.addListener(_checkRequiredFields);
    nameInEnglishController.addListener(_checkRequiredFields);
    nameInArabicController.addListener(_checkRequiredFields);
    titleInEnglishController.addListener(_checkRequiredFields);
    titleInArabicController.addListener(_checkRequiredFields);
    mobileNumberController.addListener(_checkRequiredFields);
    directNumberController.addListener(_checkRequiredFields);
    remarksInEnglishController.addListener(_checkRequiredFields);
    remarksInArabicController.addListener(_checkRequiredFields);
    travelStartDateController.addListener(_checkRequiredFields);
    travelEndDateController.addListener(_checkRequiredFields);
    ref.listen<String?>(countryProvider, (previous, next) {
      _checkRequiredFields();
    });
  }

  @override
  void dispose() {
    accountNumberController.dispose();
    ibanNumberController.dispose();
    bankNameController.dispose();
    branchNameController.dispose();
    addressedToController.dispose();
    nameInEnglishController.dispose();
    nameInArabicController.dispose();
    titleInEnglishController.dispose();
    titleInArabicController.dispose();
    mobileNumberController.dispose();
    directNumberController.dispose();
    remarksInEnglishController.dispose();
    remarksInArabicController.dispose();
    linkController.dispose();
    super.dispose();
  }

  void _checkRequiredFields() {
    setState(() {
      allRequiredFieldsFilled = _areRequiredFieldsFilled(
        ref.read(requestDetailProvider(widget.letterRequestType)),
      );
    });
  }

  bool _areRequiredFieldsFilled(RequestDetailState state) {
    if (state.letterRequestType.documentType!.isEmpty) {
      return false;
    }

    if (state.letterRequestType.documentType! == "Request Bank Change") {
      return accountNumberController.text.isNotEmpty &&
          ibanNumberController.text.isNotEmpty &&
          bankNameController.text.isNotEmpty &&
          branchNameController.text.isNotEmpty;
    } else if (state.letterRequestType.documentType! ==
        "Request Business Card") {
      return nameInEnglishController.text.isNotEmpty &&
          nameInArabicController.text.isNotEmpty &&
          titleInEnglishController.text.isNotEmpty &&
          titleInArabicController.text.isNotEmpty &&
          mobileNumberController.text.isNotEmpty;
    } else if (state.letterRequestType.documentType! ==
        "Request Embassy Letter") {
      return ref.read(countryProvider)?.isNotEmpty == true &&
          travelStartDateController.text.isNotEmpty &&
          travelEndDateController.text.isNotEmpty;
    } else if (state.letterRequestType.documentType! ==
        "Request Salary Certificate") {
      return addressedToController.text.isNotEmpty;
    }
    return false;
  }

  void _clearAllControllers() {
    final controllers = [
      accountNumberController,
      ibanNumberController,
      bankNameController,
      branchNameController,
      swiftCodeController,
      addressedToController,
      nameInEnglishController,
      nameInArabicController,
      titleInEnglishController,
      titleInArabicController,
      mobileNumberController,
      directNumberController,
      remarksInEnglishController,
      remarksInArabicController,
      linkController,
      travelStartDateController,
      travelEndDateController,
      // salaryCertificateDateController,
    ];

    for (var controller in controllers) {
      controller.clear();
    }
  }

  void _resetFormState() {
    // Generate a new key to force complete rebuild of the form
    setState(() {
      _formKey = UniqueKey();
    });

    // Clear all controllers
    _clearAllControllers();

    // Reset other state variables
    _uploadedFiles = [];
    allRequiredFieldsFilled = false;

    // Clear providers
    ref.read(linkListProvider.notifier).clear();

    // Reset country provider if it exists
    if (ref.exists(countryProvider)) {
      ref.read(countryProvider.notifier).state = null;
    }
  }

  String _getFilterType() {
    switch (widget.originContext) {
      case DocumentContext.salary:
        return 'salary';
      case DocumentContext.noc:
        return 'noc';
      case DocumentContext.all:
      default:
        return 'all';
    }
  }

  String _getAppBarTitle() {
    switch (widget.originContext) {
      case DocumentContext.noc:
        return 'No objection certificate';
      case DocumentContext.salary:
        return 'Salary information letter';
      case DocumentContext.all:
      default:
        return 'Request letter';
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(requestDetailProvider(widget.letterRequestType));
    final notifier = ref.read(
      requestDetailProvider(widget.letterRequestType).notifier,
    );

    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return GestureDetector(
      onTap: () {
        if (isDropdownOpen) {
          setState(() => isDropdownOpen = false);
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: CustomAppBar(
          title: _getAppBarTitle(),
          showActionIcon: true,
          onBackPressed: () {
            Navigator.pop(context);
          },
        ),
        body: Center(
          child: Container(
            width:
                MediaQuery.of(context).size.width > 600 ? 600 : double.infinity,
            child: Padding(
              padding: const EdgeInsets.only(
                left: 20,
                right: 20,
                top: 16,
                bottom: 20,
              ),
              child: Column(
                children: [
                  Expanded(
                    child: Form(
                      key: _formKey, // Use the dynamic key here
                      child: ListView(
                        children: [
                          HeadingText(text: 'Document type', hasAsterisk: true),
                          SizedBox(height: 12),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                isDropdownOpen = true;
                              });
                              showRequestLetterdetailscreenBottomSheet(
                                context: context,
                                ref: ref,
                                currentValue:
                                    state.letterRequestType.documentType!,

                                onTypeSelected: (
                                  LetterRequestTypesModel value,
                                ) {
                                  setState(() {
                                    isDropdownOpen = false;
                                  });
                                  if (value.documentType !=
                                      state.letterRequestType.documentType) {
                                    // Reset form state when document type changes
                                    _resetFormState();

                                    // Update the request type
                                    notifier.updateRequestType(value);
                                  }
                                },
                                filterType: _getFilterType(),
                                onToggleDropdown: () {
                                  setState(() {
                                    isDropdownOpen = !isDropdownOpen;
                                  });
                                },
                              );
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  OpenSansText(
                                    capitalizeFirstWordOnly(
                                      state.letterRequestType.documentType!,
                                    ),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.black,
                                  ),
                                  CustomSvgImage(
                                    imageName:
                                        isDropdownOpen
                                            ? "Arrowup"
                                            : "ArrowLeft",
                                    color: AppColors.viewColor,
                                    height: 16,
                                    width: 16,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Use the key here to force rebuild of RequestFormContent
                          RequestFormContent(
                            key: _formKey,
                            letterRequestType: state.letterRequestType,
                            screenHeight: screenHeight,
                            uploadedFiles: _uploadedFiles,
                            onFilesChanged: (files) {
                              setState(() {
                                _uploadedFiles = files;
                              });
                            },
                            accountNumberController: accountNumberController,
                            ibanNumberController: ibanNumberController,
                            bankNameController: bankNameController,
                            branchNameController: branchNameController,
                            swiftCodeController: swiftCodeController,
                            nameInEnglishController: nameInEnglishController,
                            nameInArabicController: nameInArabicController,
                            titleInEnglishController: titleInEnglishController,
                            titleInArabicController: titleInArabicController,
                            mobileNumberController: mobileNumberController,
                            directNumberController: directNumberController,
                            remarksInEnglishController:
                                remarksInEnglishController,
                            remarksInArabicController:
                                remarksInArabicController,
                            addressedToController: addressedToController,
                            travelStartDate: travelStartDateController,
                            travelEndDate: travelEndDateController,
                            // salaryCertificateDate: salaryCertificateDateController,
                            link: linkController,
                          ),
                        ],
                      ),
                    ),
                  ),
                  SubmitButtonRequestForm(
                    screenWidth: screenWidth,
                    screenHeight: screenHeight,
                    allRequiredFieldsFilled: allRequiredFieldsFilled,
                    uploadedFiles: _uploadedFiles,
                    countryProvider: countryProvider,
                    state: state,
                    ref: ref,
                    context: context,

                    accountNumberController: accountNumberController,
                    ibanNumberController: ibanNumberController,
                    bankNameController: bankNameController,
                    branchNameController: branchNameController,
                    swiftCodeController: swiftCodeController,
                    nameInEnglishController: nameInEnglishController,
                    nameInArabicController: nameInArabicController,
                    titleInEnglishController: titleInEnglishController,
                    titleInArabicController: titleInArabicController,
                    mobileNumberController: mobileNumberController,
                    directNumberController: directNumberController,
                    remarksInEnglishController: remarksInEnglishController,
                    remarksInArabicController: remarksInArabicController,
                    travelStartDateController: travelStartDateController,
                    travelEndDateController: travelEndDateController,
                    addressedToController: addressedToController,
                    // salaryCertificateDateController: salaryCertificateDateController,
                    linkController: linkController,
                  ),
                  if (state.letterRequestType.documentType! ==
                          "Request Embassy Letter" ||
                      state.letterRequestType.documentType! ==
                          "Request Salary Certificate" ||
                      state.letterRequestType.documentType! ==
                          "Request Salary Transfer" ||
                      state.letterRequestType.documentType! ==
                          "Request Medical Fitness Letter" ||
                      state.letterRequestType.documentType! !=
                          "Request Residency Letter" ||
                      state.letterRequestType.documentType! ==
                          "Request NOC for Drivers License")
                    ViewdraftbuttonRequestform(
                      screenWidth: screenWidth,
                      screenHeight: screenHeight,
                      allRequiredFieldsFilled: allRequiredFieldsFilled,
                      uploadedFiles: _uploadedFiles,
                      countryProvider: countryProvider,
                      state: state,
                      ref: ref,
                      context: context,
                      // personNumber: masriPersonId,
                      travelStartDateController: travelStartDateController,
                      travelEndDateController: travelEndDateController,
                      addressedToController: addressedToController,
                      // salaryCertificateDateController: salaryCertificateDateController,
                      linkController: linkController,
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
